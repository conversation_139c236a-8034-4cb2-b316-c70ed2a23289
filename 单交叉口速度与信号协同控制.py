import traci
import pandas as pd
import sumolib
import random
import warnings
warnings.filterwarnings("ignore")
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.font_manager import FontProperties
font = FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf", size=10)

#相位剩余时间
def phase_remain_time(tls_id):
    global remain_time
    simulation_time=traci.simulation.getTime()
    next_switch_time=traci.trafficlight.getNextSwitch(tls_id)
    phase_id=traci.trafficlight.getPhase(tls_id)
    remain_time=next_switch_time-simulation_time
    return remain_time

#获取信号灯各相位持续时间
def get_duration_time(tls_id):
    tl_def = traci.trafficlight.getAllProgramLogics(tls_id)
    program = traci.trafficlight.getProgram(tls_id)
    obj_logic = [_i for _i in tl_def if _i.getSubID() == program][0]
    phase_duration = []
    for phase in obj_logic.phases:
        a=phase.duration
        phase_duration.append(a)
    return phase_duration

#获取下周期绿灯时间
def next_phase_green_time(tls,a,b):
    if b < a:
        result=sum(tls[b:a])
    else:
        result=sum(tls[b:])+sum(tls[:a])
    return result

#获取速度控制公交前方的社会车排队长度
def car_num_before_bus(bus_id):
    lane_id = traci.vehicle.getLaneID(bus_id)
    car_id_before_bus = traci.lane.getLastStepVehicleIDs(lane_id)
    for i in car_id_before_bus:
        v_type = traci.vehicle.getTypeID(i)
        if v_type =="car":
            car_before_bus.append(i)
    car_before_bus_length = len(car_before_bus)*(car_length + car_gaps)
    return car_before_bus_length


#轨迹推算1
def trajectory_estimation1(lane1_id,bus_id,next_phase_time):

    #获取车辆在路段上的剩余位置
    remain_distance = traci.lane.getLength(lane1_id) - traci.vehicle.getLanePosition(bus_id)
    if remain_time != 0:
        predict_distance = remain_time*traci.vehicle.getSpeed(bus_id)
        predict_distance_max = remain_time * bus_max_speed
        if predict_distance >= remain_distance or predict_distance_max >= remain_distance:
            traci.vehicle.setSpeed(bus_id,bus_max_speed)
        else:
            #计算减速点
            dec_point = (remain_time + signal_cycle) * bus_min_speed
            Dec_point[bus_id] = dec_point

    else:
        predict_distance = next_phase_time * traci.vehicle.getSpeed(bus_id)
        if predict_distance <= remain_distance:
            traci.vehicle.setSpeed(bus_id,bus_min_speed)
        else:
            #计算减速点
            dec_point = next_phase_time*bus_min_speed
            Dec_point[bus_id] = dec_point

    return Dec_point

#轨迹推算1
def trajectory_estimation2(lane1_id,bus_id,next_phase_time):
    #获取车辆在路段上的剩余位置
    remain_distance = traci.lane.getLength(lane1_id) - traci.vehicle.getLanePosition(bus_id)
    if remain_time != 0:
        predict_distance = remain_time*traci.vehicle.getSpeed(bus_id)
        predict_distance_max = remain_time * bus_max_speed
        if predict_distance >= remain_distance or predict_distance_max >= remain_distance:
            pass
        else:
            #计算减速点
            dec_point = (remain_time + next_phase_time) * bus_min_speed
            Dec_point[bus_id] = dec_point

    else:
        predict_distance = next_phase_time * traci.vehicle.getSpeed(bus_id)
        if predict_distance <= remain_distance:
            pass
        else:
            #计算减速点
            dec_point = next_phase_time*bus_min_speed
            Dec_point[bus_id] = dec_point

    return Dec_point

#1-公交速度控制
def speed_control(dec_point,bus_id,lane1_id,remain_time):
    #路段长度
    road_length1=traci.lane.getLength(lane1_id)
    #距离路口的距离
    remain_distance = road_length1- traci.vehicle.getLanePosition(bus_id)
    if dec_point is None:
        traci.vehicle.setSpeed(bus_id,bus_min_speed)
    elif dec_point <= road_length1:
        if road_length1 - traci.vehicle.getLanePosition(bus_id) <= dec_point:
            traci.vehicle.setSpeed(bus_id,bus_min_speed)
    else:
        dec_point2=dec_point-traci.lane.getLength(lane1_id)
        if road_length1-traci.vehicle.getLanePosition(bus_id) > dec_point2:
            traci.vehicle.setSpeed(bus_id,bus_max_speed)



# 1-车路协同控制
signal_plan_after3 = pd.read_csv('优化后的信号配时方案1.csv')
after3_trigger_time_list = signal_plan_after3['Cycle'].unique().tolist()

# 连接sumo
sumo_binary = "C:/Program Files (x86)/Eclipse/Sumo/bin/sumo-gui.exe"
sumo_cfg_file = "NormalIntersection20%.sumocfg"
traci.start([sumo_binary, "-c", sumo_cfg_file])

# 全局phase(三号交叉口)定义
phase_dict = {'1': 'rrrrGGGrrrrrGGGr',
                '2': 'rrrrGrrGrrrrGrrG',
                '3': 'GGGrrrrrGGGrrrrr',
                '4': 'GrrGrrrrGrrGrrrr'}

junction_id3 = "light3"
after3_phase_duration_dict = signal_plan_after3.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()
after3_logic_dict = {}
for phase_time, duration_dict in after3_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))
    after3_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#初始信号配时开始仿真
car_length = 5
car_gaps = 3
bus_max_speed=14
bus_min_speed=4
signal_cycle=90
person_num_bus=random.randint(25,35)
person_num_car=1.5
vehicle_tracking_after3=[]
track_cycle = []

current_time = 0
cycle_start_time=current_time

car_count_after3 = []
bus_count_after3 = []

cycles_after3=[]
car_in_cycle_after3=[]
bus_in_cycle_after3=[]
person_in_cycle_after3=[]

next_phase_time=0
# 被控公交列表
controled_bus=[]
#公交前方的社会车
car_before_bus = []
#公交减速点字典
Dec_point={}

#计算乘客延误时间
total_person_delay_after3=0
bus_delay_after3 = 0
cycle_delay_after3 = 0
last_cycle_after3 = 0
simulation_delay_time_after3 = []
average_delay_time_after3 = []
bus_delay_time_after3 = []

phase_to_lane={"1":["phase3_1_2","phase3_1_1","phase3_3_1","phase3_3_2"],
                 "2":["phase3_1_3","phase3_3_3"],
                 "3":["phase3_2_1","phase3_2_2","phase3_4_2","phase3_4_1"],
                 "4":["phase3_4_3","phase3_2_3"]}


vehicle_data = {}
# while traci.simulation.getMinExpectedNumber() > 0:
while traci.simulation.getTime()<5000:
    cycle_counter = current_time//signal_cycle + 1

    # 仿真开始前输入信号配时方案
    if current_time == 0:
        # global phase_time
        for phase_time, logic in after3_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案（每周期切换一次）
    if cycle_counter in after3_trigger_time_list:
        if cycle_counter not in track_cycle:
            traci.trafficlight.setProgram('light3', str(cycle_counter))
            track_cycle.append(cycle_counter)

    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #优先相位剩余绿灯时间计算(0相位)
    phase_duration = get_duration_time(junction_id3)
    # print(f"周期{cycle_counter}信号配时方案：{phase_duration}")
    # 获取优先相位剩余绿灯时间（0相位）
    if traci.trafficlight.getPhase(junction_id3) == 0:
        remain_time = phase_remain_time(junction_id3)
        print(f"优先相位剩余绿灯时间:{remain_time}")

    else:
        phase = traci.trafficlight.getPhase(junction_id3)
        remain_time2 = phase_remain_time(junction_id3)
        remain_time = 0
        next_phase_time = next_phase_green_time(phase_duration,0,phase) - phase_duration[phase] + remain_time2
        print(f"下一相位切换时间:{next_phase_time}")

    #获取优先相位方向路段上的所有车辆id （0相位）
    vehicle_ids = traci.edge.getLastStepVehicleIDs("phase3_1") + traci.edge.getLastStepVehicleIDs("phase3_3")
    #遍历每个车辆id，将公交车筛选出来（0相位）
    for vehicle_id in vehicle_ids:
        vehicle_type = traci.vehicle.getTypeID(vehicle_id)
        if vehicle_type =="bus":
            if vehicle_id not in controled_bus:
                orginal_lane = traci.vehicle.getLaneID(vehicle_id)
                Dec_point = trajectory_estimation1(orginal_lane,vehicle_id,next_phase_time)
                controled_bus.append(vehicle_id)

        #南进口道直行
        vehicle_list = traci.lane.getLastStepVehicleIDs('phase3_1_2') + traci.lane.getLastStepVehicleIDs('phase3_1_1')
        for i in vehicle_list:
            bus_type = traci.vehicle.getTypeID(i)
            if remain_time != 0 and bus_type == "bus":
                remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i)- car_num_before_bus(i)
                # print(f"剩余距离：{remain_diatance}米")
                diatance_max = remain_time * bus_max_speed
                # print(f"最大速度行驶距离：{diatance_max}米")
                diatance_min = remain_time * bus_min_speed
                # print(f"最小速度行驶距离：{diatance_min}米")

                if diatance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i,bus_max_speed)
                    # print(f"绿灯时，最小速度能通过，设置最大速度")
                elif diatance_min <= remain_diatance <= diatance_max:
                    traci.vehicle.setSpeed(i,bus_max_speed)
                    # print(f"最大速度能通过，设置最大速度")
                else:
                    traci.vehicle.setSpeed(i,bus_min_speed)
                    # print(f"本周期不能通过，以最小速度")
            elif remain_time == 0 and bus_type == "bus":
                #当前相位为红灯，减速行驶
                remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time
                distance_min = bus_min_speed * next_phase_time
                #以最小速度行驶都不能通过，只能等待，设置最小速度
                if distance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i,bus_min_speed)
                    # print(f"红灯时，以最小速度行驶不能等到绿灯，设置最小速度")
                elif distance_min <= remain_diatance <= distance_max:
                    traci.vehicle.setSpeed(i,bus_min_speed)
                    # print(f"红灯时，以最小速度能等到绿灯，设置最小速度")
                elif distance_max <= remain_diatance:
                    traci.vehicle.setSpeed(i,bus_max_speed)
                    # print(f"红灯时，以最大速度能等到绿灯，设置最大速度")

        #北进口道直行
        vehicle_list = traci.lane.getLastStepVehicleIDs('phase3_3_1') + traci.lane.getLastStepVehicleIDs('phase3_3_2')+ traci.lane.getLastStepVehicleIDs('phase3_3_3')
        for i in vehicle_list:
            bus_type = traci.vehicle.getTypeID(i)
            if remain_time != 0 and bus_type == "bus":
                #任意一条边计算道路长度
                remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                # print(f"剩余距离：{remain_diatance}米")
                diatance_max = remain_time * bus_max_speed
                # print(f"最大速度行驶距离：{diatance_max}米")
                diatance_min = remain_time * bus_min_speed
                # print(f"最小速度行驶距离：{diatance_min}米")

                if diatance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                    # print(f"绿灯时，最小速度能通过，设置最大速度")
                elif diatance_min <= remain_diatance <= diatance_max:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                    # print(f"最大速度能通过，设置最大速度")
                else:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                    # print(f"本周期不能通过，以最小速度")
            elif remain_time == 0 and bus_type == "bus":
                # 当前相位为红灯，减速行驶
                remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time
                distance_min = bus_min_speed * next_phase_time
                # 以最小速度行驶都不能通过，只能等待，设置最小速度
                if distance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                    # print(f"红灯时，以最小速度行驶不能等到绿灯，设置最小速度")
                elif distance_min <= remain_diatance <= distance_max:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                    # print(f"红灯时，以最小速度能等到绿灯，设置最小速度")
                elif distance_max <= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)


    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_after3 and vehicle_speed < 0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car":
                            total_person_delay_car = vehicle_waiting_time * person_num_car
                            total_person_delay_after3 += total_person_delay_car
                            cycle_delay_after3 += total_person_delay_car

                            car_count_after3.append(vehicle_id)
                            vehicle_tracking_after3.append(vehicle_id)

                        elif vehicle_type=="bus":
                            #总乘客延误时间
                            total_person_delay_bus = vehicle_waiting_time * person_num_bus
                            total_person_delay_after3 += total_person_delay_bus
                            cycle_delay_after3 += total_person_delay_bus
                            #公交车延误时间
                            bus_delay_after3 += vehicle_waiting_time
                            #延误公交车数量
                            bus_count_after3.append(vehicle_id)
                            vehicle_tracking_after3.append(vehicle_id)
                    elif vehicle_id in vehicle_tracking_after3 and vehicle_speed >0.1:
                        vehicle_tracking_after3.remove(vehicle_id)

    if current_time > time_interval_end:
        #统计周期结束时数据
        car_count_cycle_after3 = len(car_count_after3)
        bus_count_cycle_after3 = len(bus_count_after3)
        person_count_cycle_after3 = int(car_count_cycle_after3*person_num_car + bus_count_cycle_after3*person_num_bus)
        person_delay_time_after3 = int(cycle_delay_after3)
        average_delay_after3 = int(person_delay_time_after3/person_count_cycle_after3)
        bus_delay_cycle_after3 = int(bus_delay_after3)

        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_after3}, Buses={bus_count_cycle_after3}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_after3}")

        cycles_after3.append(cycle_counter)
        car_in_cycle_after3.append(car_count_cycle_after3)
        bus_in_cycle_after3.append(bus_count_cycle_after3)
        person_in_cycle_after3.append(person_count_cycle_after3)
        simulation_delay_time_after3.append(person_delay_time_after3)
        average_delay_time_after3.append(average_delay_after3)
        bus_delay_time_after3.append(bus_delay_cycle_after3)

        car_count_after3 = []
        bus_count_after3 = []
        cycle_delay_after3 = 0
        bus_delay_after3 = 0
        cycle_start_time = time_interval_end

traci.close()

#2-仅信号控制
# 配时方案
signal_plan_after1 = pd.read_csv('优化后的信号配时方案1.csv')
after1_trigger_time_list = signal_plan_after1['Cycle'].unique().tolist()

traci.start([sumo_binary, "-c", sumo_cfg_file])

after1_phase_duration_dict = signal_plan_after1.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

after1_logic_dict = {}

for phase_time, duration_dict in after1_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))
    after1_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#优化信号配时方案开始仿真
vehicle_tracking_after1=[]
cycle_counter=1
current_time = 0
cycle_start_time=current_time
track_cycle = []

car_count_after1 = []
bus_count_after1 = []

cycles_after1=[]
car_in_cycle_after1=[]
bus_in_cycle_after1=[]
person_in_cycle_after1=[]

#计算乘客延误时间
total_person_delay_after1=0
bus_delay_after1 = 0
cycle_delay_after1 = 0
last_cycle_after1 = 0
simulation_delay_time_after1 = []
average_delay_time_after1 = []
bus_delay_time_after1 = []

# while traci.simulation.getMinExpectedNumber() > 0:
while traci.simulation.getTime()<5000:
    cycle_counter =current_time // signal_cycle + 1
    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in after1_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案
    if cycle_counter in after1_trigger_time_list:
        if cycle_counter not in track_cycle:
            traci.trafficlight.setProgram('light3', str(cycle_counter))
            track_cycle.append(cycle_counter)
    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_after1 and vehicle_speed<0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car":
                            total_person_delay_car = vehicle_waiting_time * person_num_car
                            total_person_delay_after1 += total_person_delay_car
                            cycle_delay_after3 += total_person_delay_car

                            car_count_after1.append(vehicle_id)
                            vehicle_tracking_after1.append(vehicle_id)

                        elif vehicle_type=="bus":
                            #总乘客延误时间
                            total_person_delay_bus = vehicle_waiting_time * person_num_bus
                            total_person_delay_after1 += total_person_delay_bus
                            cycle_delay_after1 += total_person_delay_bus
                            #公交延误时间
                            bus_delay_after1 += vehicle_waiting_time
                            #延误公交车数量
                            bus_count_after1.append(vehicle_id)
                            vehicle_tracking_after1.append(vehicle_id)
                    elif vehicle_id in vehicle_tracking_after1 and vehicle_speed >0.1:
                        vehicle_tracking_after1.remove(vehicle_id)

    if current_time > time_interval_end:
        #统计周期结束时的车辆数量
        car_count_cycle_after1=len(car_count_after1)
        bus_count_cycle_after1=len(bus_count_after1)
        person_count_cycle_after1=int(car_count_cycle_after1*person_num_car+bus_count_cycle_after1*person_num_bus)
        person_delay_time_after1 = int(cycle_delay_after1)
        average_delay_after1 = int(person_delay_time_after1/person_count_cycle_after1)
        bus_delay_cycle_after1 = int(bus_delay_after1)

        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_after1}, Buses={bus_count_cycle_after1}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_after1}")

        cycles_after1.append(cycle_counter)
        car_in_cycle_after1.append(car_count_cycle_after1)
        bus_in_cycle_after1.append(bus_count_cycle_after1)
        person_in_cycle_after1.append(person_count_cycle_after1)
        simulation_delay_time_after1.append(person_delay_time_after1)
        average_delay_time_after1.append(average_delay_after1)
        bus_delay_time_after1.append(bus_delay_cycle_after1)

        car_count_after1 = []
        bus_count_after1 = []
        cycle_delay_after1 = 0
        bus_delay_after1 = 0
        cycle_start_time = time_interval_end

traci.close()

#3-初始信号配时方案
# 配时方案
signal_plan_before = pd.read_csv('初始信号配时方案1.csv')
before_trigger_time_list = signal_plan_before['Cycle'].unique().tolist()

traci.start([sumo_binary, "-c", sumo_cfg_file])

before_phase_duration_dict = signal_plan_before.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

before_logic_dict = {}

for phase_time, duration_dict in before_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))

    before_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})


vehicle_tracking_before=[]
cycle_counter=1
current_time = 0
cycle_start_time=current_time
track_cycle = []

car_count_before = []
bus_count_before = []

cycles_before=[]
car_in_cycle_before=[]
bus_in_cycle_before=[]
person_in_cycle_before=[]

#计算乘客延误时间
total_person_delay_before=0
bus_delay_before = 0
cycle_delay_before = 0
last_cycle_before = 0
simulation_delay_time_before = []
average_delay_time_before = []
bus_delay_time_before = []
# while traci.simulation.getMinExpectedNumber() > 0:
while traci.simulation.getTime()<5000:
    cycle_counter =current_time // signal_cycle + 1
    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in before_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案
    if cycle_counter in before_trigger_time_list:
        if cycle_counter not in track_cycle:
            traci.trafficlight.setProgram('light3', str(cycle_counter))
            track_cycle.append(cycle_counter)
    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_before and vehicle_speed<0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car":
                            total_person_delay_car = vehicle_waiting_time * person_num_car
                            total_person_delay_before += total_person_delay_car
                            cycle_delay_before += total_person_delay_car

                            car_count_before.append(vehicle_id)
                            vehicle_tracking_before.append(vehicle_id)

                        elif vehicle_type=="bus":
                            #总乘客延误时间
                            total_person_delay_bus = vehicle_waiting_time * person_num_bus
                            total_person_delay_before += total_person_delay_bus
                            cycle_delay_before += total_person_delay_bus
                            #公交车延误时间
                            bus_delay_before += vehicle_waiting_time
                            #延误社会车数量
                            bus_count_before.append(vehicle_id)
                            vehicle_tracking_before.append(vehicle_id)

                    elif vehicle_id in vehicle_tracking_before and vehicle_speed >0.1:
                        vehicle_tracking_before.remove(vehicle_id)

    if current_time > time_interval_end:
        #统计周期结束时的车辆数量
        car_count_cycle_before=len(car_count_before)
        bus_count_cycle_before=len(bus_count_before)
        person_count_cycle_before=int(car_count_cycle_before*person_num_car+bus_count_cycle_before*person_num_bus)
        person_delay_time_before = int(cycle_delay_before)
        average_delay_before = int(person_delay_time_before/person_count_cycle_before)
        bus_delay_cycle_before = int(bus_delay_before)
        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_before}, Buses={bus_count_cycle_before}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_before}")

        cycles_before.append(cycle_counter)
        car_in_cycle_before.append(car_count_cycle_before)
        bus_in_cycle_before.append(bus_count_cycle_before)
        person_in_cycle_before.append(person_count_cycle_before)
        simulation_delay_time_before.append(person_delay_time_before)
        average_delay_time_before.append(average_delay_before)
        bus_delay_time_before.append(bus_delay_cycle_before)

        car_count_before = []
        bus_count_before = []
        cycle_delay_before = 0
        bus_delay_before = 0
        cycle_start_time = time_interval_end

traci.close()


#仅速度控制
# 配时方案
signal_plan_after2 = pd.read_csv('初始信号配时方案1.csv')
after2_trigger_time_list = signal_plan_after2['Cycle'].unique().tolist()

traci.start([sumo_binary, "-c", sumo_cfg_file])

after2_phase_duration_dict = signal_plan_after2.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

after2_logic_dict = {}

for phase_time, duration_dict in after2_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))

    after2_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#优化信号配时方案开始仿真
vehicle_tracking_after2=[]
cycle_counter=1
current_time = 0
cycle_start_time=current_time
track_cycle = []

car_count_after2 = []
bus_count_after2 = []

cycles_after2=[]
car_in_cycle_after2=[]
bus_in_cycle_after2=[]
person_in_cycle_after2=[]

next_phase_time_after_2=0
# 被控公交列表
controled_bus_after_2=[]
#公交前方的社会车
car_before_bus_after_2 = []
#公交减速点字典
Dec_point_after_2={}

#计算乘客延误时间
total_person_delay_after_2=0
bus_delay_after_2 = 0
cycle_delay_after_2 = 0
last_cycle_after_2 = 0
simulation_delay_time_after_2 = []
average_delay_time_after_2 = []
bus_delay_time_after_2 = []

# while traci.simulation.getMinExpectedNumber() > 0:
while traci.simulation.getTime()<5000:
    cycle_counter =current_time // signal_cycle + 1
    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in after2_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案
    if cycle_counter in after2_trigger_time_list:
        if cycle_counter not in track_cycle:
            traci.trafficlight.setProgram('light3', str(cycle_counter))
            track_cycle.append(cycle_counter)
    # 开始仿真
    traci.simulationStep()
    current_time += 1

    # 优先相位剩余绿灯时间计算(0相位)
    phase_duration = get_duration_time(junction_id3)
    # print(f"周期{cycle_counter}信号配时方案：{phase_duration}")
    # 获取优先相位剩余绿灯时间（0相位）
    if traci.trafficlight.getPhase(junction_id3) == 0:
        remain_time = phase_remain_time(junction_id3)
        print(f"优先相位剩余绿灯时间:{remain_time}")

    else:
        phase = traci.trafficlight.getPhase(junction_id3)
        remain_time2 = phase_remain_time(junction_id3)
        remain_time = 0
        next_phase_time = next_phase_green_time(phase_duration, 0, phase) - phase_duration[phase] + remain_time2
        print(f"下一相位切换时间:{next_phase_time}")

    # 获取优先相位方向路段上的所有车辆id （0相位）
    vehicle_ids = traci.edge.getLastStepVehicleIDs("phase3_1") + traci.edge.getLastStepVehicleIDs("phase3_3")
    # 遍历每个车辆id，将公交车筛选出来（0相位）
    for vehicle_id in vehicle_ids:
        vehicle_type = traci.vehicle.getTypeID(vehicle_id)
        if vehicle_type == "bus":
            if vehicle_id not in controled_bus_after_2:
                orginal_lane = traci.vehicle.getLaneID(vehicle_id)
                Dec_point = trajectory_estimation1(orginal_lane, vehicle_id, next_phase_time)
                controled_bus_after_2.append(vehicle_id)

        # 南进口道直行
        vehicle_list = traci.lane.getLastStepVehicleIDs('phase3_1_2') + traci.lane.getLastStepVehicleIDs('phase3_1_1')
        for i in vehicle_list:
            bus_type = traci.vehicle.getTypeID(i)
            if remain_time != 0 and bus_type == "bus":
                remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                # print(f"剩余距离：{remain_diatance}米")
                diatance_max = remain_time * bus_max_speed
                # print(f"最大速度行驶距离：{diatance_max}米")
                diatance_min = remain_time * bus_min_speed
                # print(f"最小速度行驶距离：{diatance_min}米")

                if diatance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                    # print(f"绿灯时，最小速度能通过，设置最大速度")
                elif diatance_min <= remain_diatance <= diatance_max:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                    # print(f"最大速度能通过，设置最大速度")
                else:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                    # print(f"本周期不能通过，以最小速度")
            elif remain_time == 0 and bus_type == "bus":
                # 当前相位为红灯，减速行驶
                remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time
                distance_min = bus_min_speed * next_phase_time
                # 以最小速度行驶都不能通过，只能等待，设置最小速度
                if distance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                    # print(f"红灯时，以最小速度行驶不能等到绿灯，设置最小速度")
                elif distance_min <= remain_diatance <= distance_max:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                    # print(f"红灯时，以最小速度能等到绿灯，设置最小速度")
                elif distance_max <= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                    # print(f"红灯时，以最大速度能等到绿灯，设置最大速度")

        # 北进口道直行
        vehicle_list = traci.lane.getLastStepVehicleIDs('phase3_3_1') + traci.lane.getLastStepVehicleIDs(
            'phase3_3_2') + traci.lane.getLastStepVehicleIDs('phase3_3_3')
        for i in vehicle_list:
            bus_type = traci.vehicle.getTypeID(i)
            if remain_time != 0 and bus_type == "bus":
                # 任意一条边计算道路长度
                remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                # print(f"剩余距离：{remain_diatance}米")
                diatance_max = remain_time * bus_max_speed
                # print(f"最大速度行驶距离：{diatance_max}米")
                diatance_min = remain_time * bus_min_speed
                # print(f"最小速度行驶距离：{diatance_min}米")

                if diatance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                    # print(f"绿灯时，最小速度能通过，设置最大速度")
                elif diatance_min <= remain_diatance <= diatance_max:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                    # print(f"最大速度能通过，设置最大速度")
                else:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                    # print(f"本周期不能通过，以最小速度")
            elif remain_time == 0 and bus_type == "bus":
                # 当前相位为红灯，减速行驶
                remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time
                distance_min = bus_min_speed * next_phase_time
                # 以最小速度行驶都不能通过，只能等待，设置最小速度
                if distance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                    # print(f"红灯时，以最小速度行驶不能等到绿灯，设置最小速度")
                elif distance_min <= remain_diatance <= distance_max:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                    # print(f"红灯时，以最小速度能等到绿灯，设置最小速度")
                elif distance_max <= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)


    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_after2 and vehicle_speed<0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car":
                            total_person_delay_car = vehicle_waiting_time * person_num_car
                            total_person_delay_after_2 += total_person_delay_car
                            cycle_delay_after_2 += total_person_delay_car

                            car_count_after2.append(vehicle_id)
                            vehicle_tracking_after2.append(vehicle_id)

                        elif vehicle_type=="bus":
                            # 总乘客延误时间
                            total_person_delay_bus = vehicle_waiting_time * person_num_bus
                            total_person_delay_after_2 += total_person_delay_bus
                            cycle_delay_after_2 += total_person_delay_bus
                            # 公交车延误时间
                            bus_delay_after_2 += vehicle_waiting_time
                            # 延误社会车数量
                            bus_count_after2.append(vehicle_id)
                            vehicle_tracking_after2.append(vehicle_id)

                    elif vehicle_id in vehicle_tracking_before and vehicle_speed >0.1:
                        vehicle_tracking_before.remove(vehicle_id)

    if current_time > time_interval_end:
        #统计周期结束时的车辆数量
        car_count_cycle_after2=len(car_count_after2)
        bus_count_cycle_after2=len(bus_count_after2)
        person_count_cycle_after2=int(car_count_cycle_after2*person_num_car+bus_count_cycle_after2*person_num_bus)
        person_delay_time_after_2 = int(cycle_delay_after_2)
        average_delay_after_2 = int(person_delay_time_after_2/person_count_cycle_after2)
        bus_delay_cycle_after_2 = int(bus_delay_after_2)
        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_after2}, Buses={bus_count_cycle_after2}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_after2}")

        cycles_after2.append(cycle_counter)
        car_in_cycle_after2.append(car_count_cycle_after2)
        bus_in_cycle_after2.append(bus_count_cycle_after2)
        person_in_cycle_after2.append(person_count_cycle_after2)
        simulation_delay_time_after_2.append(person_delay_time_after_2)
        average_delay_time_after_2.append(average_delay_after_2)
        bus_delay_time_after_2.append(bus_delay_cycle_after_2)

        car_count_after2 = []
        bus_count_after2 = []
        cycle_delay_after_2 = 0
        bus_delay_after_2 = 0
        cycle_start_time = time_interval_end

traci.close()


#初始信号控制
cycles_before = cycles_after3[:45]
car_before = car_in_cycle_before[:45]
bus_before = bus_in_cycle_before[:45]
bus_before_delay = bus_delay_time_before[:45]
person_before = person_in_cycle_before[:45]
total_delay_time_before = simulation_delay_time_before[:45]


#仅公交优先信号控制
car_after1 = car_in_cycle_after1[:45]
bus_after1 = bus_in_cycle_after1[:45]
bus_after1_delay = bus_delay_time_after1[:45]
person_after1 = person_in_cycle_after1[:45]
total_delay_time_after1 = simulation_delay_time_after1[:45]

#仅公交速度控制
car_after2 = car_in_cycle_after2[:45]
bus_after2 = bus_in_cycle_after2[:45]
bus_after2_delay = bus_delay_time_after_2[:45]
person_after2 = person_in_cycle_after2[:45]
total_delay_time_after2 = simulation_delay_time_after_2[:45]

#车路协同控制
car_after3 = car_in_cycle_after3[:45]
bus_after3 = bus_in_cycle_after3[:45]
bus_after3_delay = bus_delay_time_after3[:45]
person_after3 = person_in_cycle_after3[:45]
total_delay_time_after3 = simulation_delay_time_after3[:45]



index = np.arange(len(cycles_before))
# 创建散点图（延误社会车数量）
plt.figure(figsize=(12, 6))
plt.scatter(index,car_before, label="初始信号", s=30, marker='o', color='gray')
plt.scatter(index,car_after1, label="公交优先信号", s=30, marker='s', color='blue')
plt.scatter(index,car_after2, label="速度控制", s=30, marker='^', color='green')
plt.scatter(index,car_after3, label="车路协同控制", s=30, marker='*', color='red')

# 用细线连接散点
plt.plot(index, car_before, linestyle='-', color='gray', alpha=0.15)
plt.plot(index, car_after1, linestyle='-', color='blue', alpha=0.15)
plt.plot(index, car_after2, linestyle='-', color='green', alpha=0.15)
plt.plot(index, car_after3, linestyle='-', color='red', alpha=0.15)

# 设置图表标题和标签
plt.xlabel("周期(cycle)" ,fontproperties=font)
plt.ylabel('延误社会车辆数(veh)' ,fontproperties=font)
plt.title('不同方案下延误社会车辆数' ,fontproperties=font)
# 设置x轴刻度
plt.xticks(index,cycles_before)
# 添加图例
plt.legend(loc='upper right', prop=font)
plt.show()


# 延误公交车数量
angles = np.linspace(0, 2*np.pi, len(cycles_before), endpoint=False).tolist()
stats_bus_before = bus_before
stats_bus_after1 = bus_after1
stats_bus_after2 = bus_after2
stats_bus_after3 = bus_after3

fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(polar=True))
ax.fill(angles, stats_bus_before, color='gray', alpha=0.25, label='初始信号')
ax.fill(angles, stats_bus_after1, color='blue', alpha=0.25, label='公交优先信号')
ax.fill(angles, stats_bus_after2, color='green', alpha=0.25, label='速度控制')
ax.fill(angles, stats_bus_after3, color='red', alpha=0.25, label='车路协同控制')

ax.set_yticklabels(["{:.0f}".format(i) for i in ax.get_yticks()])  # 添加公交车数值刻度
ax.set_xticks(angles)
ax.set_xticklabels(cycles_before)
plt.title('不同方案下延误公交车对比', fontproperties=font)
plt.legend(loc='upper right', prop=font)
plt.show()

#公交车等待时间
angles = np.linspace(0, 2*np.pi, len(cycles_before), endpoint=False).tolist()
stats_bus_before = bus_before_delay
stats_bus_after1 = bus_after1_delay
stats_bus_after2 = bus_after2_delay
stats_bus_after3 = bus_after3_delay

fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(polar=True))
ax.fill(angles, stats_bus_before, color='gray', alpha=0.25, label='初始信号')
ax.fill(angles, stats_bus_after1, color='blue', alpha=0.25, label='公交优先信号')
ax.fill(angles, stats_bus_after2, color='green', alpha=0.25, label='速度控制')
ax.fill(angles, stats_bus_after3, color='red', alpha=0.25, label='车路协同控制')

ax.set_yticklabels(["{:.0f}".format(i) for i in ax.get_yticks()])  # 添加公交车数值刻度
ax.set_xticks(angles)
ax.set_xticklabels(cycles_before)
plt.title('不同方案下公交车等待时长', fontproperties=font)
plt.legend(loc='upper right', prop=font)
plt.show()


#延误人数
data = [person_before, person_after1, person_after2, person_after3]
plt.figure(figsize=(10, 6))
box = plt.boxplot(data, patch_artist=True, medianprops=dict(color="black", linewidth=2))
colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightsalmon']

# 为每个箱体设置不同的颜色
for patch, color in zip(box['boxes'], colors):
    patch.set_facecolor(color)

# 设置中文标签
plt.xticks([1, 2, 3, 4], ['初始信号', '公交优先信号', '速度控制', '车路协同控制'], fontproperties=font)
plt.ylabel('延误人数(ped)', fontproperties=font)
plt.title('不同方案下的延误人数对比', fontproperties=font)
plt.show()


#总乘客延误时间
data = [total_delay_time_before, total_delay_time_after1, total_delay_time_after2, total_delay_time_after3]
plt.figure(figsize=(10, 6))
box = plt.boxplot(data, patch_artist=True, medianprops=dict(color="black", linewidth=2))
colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightsalmon']

# 为每个箱体设置不同的颜色
for patch, color in zip(box['boxes'], colors):
    patch.set_facecolor(color)
# 设置中文标签
plt.xticks([1, 2, 3, 4], ['初始信号', '公交优先信号', '速度控制', '车路协同控制'], fontproperties=font)
plt.ylabel('总乘客延误时长(s)', fontproperties=font)
plt.title('不同方案下的总乘客延误时长对比', fontproperties=font)
plt.show()


#人均延误时长
# data = [average_delay_time_before, average_delay_time_after1, average_delay_time_after_2, average_delay_time_after3]
# plt.figure(figsize=(10, 6))
# box = plt.boxplot(data, patch_artist=True, medianprops=dict(color="black", linewidth=2))
# colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightsalmon']
#
# # 为每个箱体设置不同的颜色
# for patch, color in zip(box['boxes'], colors):
#     patch.set_facecolor(color)
# # 设置中文标签
# plt.xticks([1, 2, 3, 4], ['初始信号', '公交优先信号', '速度控制', '车路协同控制'], fontproperties=font)
# plt.ylabel('人均延误时长(s)', fontproperties=font)
# plt.title('不同方案下的人均延误时长对比', fontproperties=font)
# plt.show()
