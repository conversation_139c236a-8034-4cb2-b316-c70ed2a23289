
# 配时方案
signal_plan_after2 = pd.read_csv('初始信号配时方案1.csv')
after_trigger_time_list = signal_plan_after2['Cycle'].unique().tolist()

traci.start([sumo_binary, "-c", sumo_cfg_file])

after_phase_duration_dict = signal_plan_after2.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

after_logic_dict = {}

for phase_time, duration_dict in after_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))

    after_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#优化信号配时方案开始仿真
vehicle_tracking_after2=[]
cycle_counter=1
current_time = 0
cycle_start_time=current_time
track_cycle = []

car_count_after2 = []
bus_count_after2 = []

cycles_after2=[]
car_in_cycle_after2=[]
bus_in_cycle_after2=[]
person_in_cycle_after2=[]

next_phase_time_after_2=0
# 被控公交列表
controled_bus_after_2=[]
#公交前方的社会车
car_before_bus_after_2 = []
#公交减速点字典
Dec_point_after_2={}

while traci.simulation.getMinExpectedNumber() > 0:
# while traci.simulation.getTime() <4060:
    cycle_counter =current_time // signal_cycle + 1
    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in after_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案
    if cycle_counter in after_trigger_time_list:
        if cycle_counter not in track_cycle:
            traci.trafficlight.setProgram('light3', str(cycle_counter))
    # 开始仿真
    traci.simulationStep()
    current_time += 1

# 优先相位剩余绿灯时间计算(0相位)
phase_duration = get_duration_time(junction_id3)
# print(f"周期{cycle_counter}信号配时方案：{phase_duration}")
# 获取优先相位剩余绿灯时间（0相位）
if traci.trafficlight.getPhase(junction_id3) == 0:
    remain_time = phase_remain_time(junction_id3)
    print(f"优先相位剩余绿灯时间:{remain_time}")

else:
    phase = traci.trafficlight.getPhase(junction_id3)
    remain_time2 = phase_remain_time(junction_id3)
    remain_time = 0
    next_phase_time = next_phase_green_time(phase_duration, 0, phase) - phase_duration[phase] + remain_time2
    print(f"下一相位切换时间:{next_phase_time}")

# 获取优先相位方向路段上的所有车辆id （0相位）
vehicle_ids = traci.edge.getLastStepVehicleIDs("phase3_1") + traci.edge.getLastStepVehicleIDs("phase3_3")
# 遍历每个车辆id，将公交车筛选出来（0相位）
for vehicle_id in vehicle_ids:
    vehicle_type = traci.vehicle.getTypeID(vehicle_id)
    if vehicle_type == "bus":
        if vehicle_id not in controled_bus_after_2:
            orginal_lane = traci.vehicle.getLaneID(vehicle_id)
            Dec_point = trajectory_estimation1(orginal_lane, vehicle_id, next_phase_time)
            controled_bus_after_2.append(vehicle_id)

    # 南进口道直行
    vehicle_list = traci.lane.getLastStepVehicleIDs('phase3_1_2') + traci.lane.getLastStepVehicleIDs('phase3_1_1')
    for i in vehicle_list:
        bus_type = traci.vehicle.getTypeID(i)
        if remain_time != 0 and bus_type == "bus":
            remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i)
            # print(f"剩余距离：{remain_diatance}米")
            diatance_max = remain_time * bus_max_speed
            # print(f"最大速度行驶距离：{diatance_max}米")
            diatance_min = remain_time * bus_min_speed
            # print(f"最小速度行驶距离：{diatance_min}米")

            if diatance_min >= remain_diatance:
                traci.vehicle.setSpeed(i, bus_max_speed)
                # print(f"绿灯时，最小速度能通过，设置最大速度")
            elif diatance_min <= remain_diatance <= diatance_max:
                traci.vehicle.setSpeed(i, bus_max_speed)
                # print(f"最大速度能通过，设置最大速度")
            else:
                traci.vehicle.setSpeed(i, bus_min_speed)
                # print(f"本周期不能通过，以最小速度")
        elif remain_time == 0 and bus_type == "bus":
            # 当前相位为红灯，减速行驶
            remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i)
            distance_max = bus_max_speed * next_phase_time
            distance_min = bus_min_speed * next_phase_time
            # 以最小速度行驶都不能通过，只能等待，设置最小速度
            if distance_min >= remain_diatance:
                traci.vehicle.setSpeed(i, bus_min_speed)
                # print(f"红灯时，以最小速度行驶不能等到绿灯，设置最小速度")
            elif distance_min <= remain_diatance <= distance_max:
                traci.vehicle.setSpeed(i, bus_min_speed)
                # print(f"红灯时，以最小速度能等到绿灯，设置最小速度")
            elif distance_max <= remain_diatance:
                traci.vehicle.setSpeed(i, bus_max_speed)
                # print(f"红灯时，以最大速度能等到绿灯，设置最大速度")

    # 北进口道直行
    vehicle_list = traci.lane.getLastStepVehicleIDs('phase3_3_1') + traci.lane.getLastStepVehicleIDs(
        'phase3_3_2') + traci.lane.getLastStepVehicleIDs('phase3_3_3')
    for i in vehicle_list:
        bus_type = traci.vehicle.getTypeID(i)
        if remain_time != 0 and bus_type == "bus":
            # 任意一条边计算道路长度
            remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i)
            # print(f"剩余距离：{remain_diatance}米")
            diatance_max = remain_time * bus_max_speed
            # print(f"最大速度行驶距离：{diatance_max}米")
            diatance_min = remain_time * bus_min_speed
            # print(f"最小速度行驶距离：{diatance_min}米")

            if diatance_min >= remain_diatance:
                traci.vehicle.setSpeed(i, bus_max_speed)
                # print(f"绿灯时，最小速度能通过，设置最大速度")
            elif diatance_min <= remain_diatance <= diatance_max:
                traci.vehicle.setSpeed(i, bus_max_speed)
                # print(f"最大速度能通过，设置最大速度")
            else:
                traci.vehicle.setSpeed(i, bus_min_speed)
                # print(f"本周期不能通过，以最小速度")
        elif remain_time == 0 and bus_type == "bus":
            # 当前相位为红灯，减速行驶
            remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i)
            distance_max = bus_max_speed * next_phase_time
            distance_min = bus_min_speed * next_phase_time
            # 以最小速度行驶都不能通过，只能等待，设置最小速度
            if distance_min >= remain_diatance:
                traci.vehicle.setSpeed(i, bus_min_speed)
                # print(f"红灯时，以最小速度行驶不能等到绿灯，设置最小速度")
            elif distance_min <= remain_diatance <= distance_max:
                traci.vehicle.setSpeed(i, bus_min_speed)
                # print(f"红灯时，以最小速度能等到绿灯，设置最小速度")
            elif distance_max <= remain_diatance:
                traci.vehicle.setSpeed(i, bus_max_speed)


    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_after2 and vehicle_speed<0.1:
                        if vehicle_type=="car":
                            car_count_after2.append(vehicle_id)
                            vehicle_tracking_after2.append(vehicle_id)

                        elif vehicle_type=="bus":
                            bus_count_after2.append(vehicle_id)
                            vehicle_tracking_after2.append(vehicle_id)

    if current_time > time_interval_end:
        #统计周期结束时的车辆数量
        car_count_cycle_after2=len(car_count_after2)
        bus_count_cycle_after2=len(bus_count_after2)
        person_count_cycle_after2=int(car_count_cycle_after2*person_num_car+bus_count_cycle_after2*person_num_bus)

        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_after2}, Buses={bus_count_cycle_after2}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_after2}")

        cycles_after2.append(cycle_counter)
        car_in_cycle_after2.append(car_count_cycle_after2)
        bus_in_cycle_after2.append(bus_count_cycle_after2)
        person_in_cycle_after2.append(person_count_cycle_after2)

        car_count_after2 = []
        bus_count_after2 = []
        cycle_start_time = time_interval_end

traci.close()
