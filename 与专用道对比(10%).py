# import traci
# import pandas as pd
# import sumolib
# import random
# import warnings
# warnings.filterwarnings("ignore")
# import matplotlib.pyplot as plt
# import numpy as np
#
#
# # 获取三号交叉口信号配时
# third_signal_plans = pd.read_csv('初始信号配时方案.csv')
# third_trigger_time_list = third_signal_plans['Cycle'].unique().tolist()
#
# # 连接sumo
# sumo_binary = "C:/Program Files (x86)/Eclipse/Sumo/bin/sumo.exe"
# sumo_cfg_file = "NormalIntersection10%.sumocfg"
# traci.start([sumo_binary, "-c", sumo_cfg_file])
#
# # 全局phase(三号交叉口)定义
# third_phase_dict = {'1': 'GrrrrGGGGrrGrrrGGGGrr',
#               '2': 'GrrrrGGrrGGGrrrGrrrGG',
#               '3': 'GGGrrGGrrrrGGGrGrrrrr',
#               '4': 'GrrGGGGrrrrGrrGGrrrrr'}
#
# third_phase_duration_dict = third_signal_plans.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()
#
# third_logic_dict = {}
#
# for phase_time, duration_dict in third_phase_duration_dict.items():
#     phase_list = []
#     for phase, state in third_phase_dict.items():
#         phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))
#
#     third_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})
#
# #开始仿真
# signal_cycle=90
# person_num_bus=random.randint(35,45)
# person_num_car=1.5
# vehicle_tracking=[]
# cycle_counter=1
# current_time = 0
# cycle_start_time=current_time
#
# car_count = []
# bus_count = []
#
# cycles=[]
# car_in_cycle=[]
# bus_in_cycle=[]
# person_in_cycle=[]
#
# phase_to_lane={
#     "1":["phase3_1_2","phase3_1_1","phase3_3_1","phase3_3_2","phase3_3_3"],
#     "2":["phase3_1_3","phase3_3_4"],
#     "3":["phase3_2_1","phase3_2_2","phase3_4_2","phase3_4_1"],
#     "4":["phase3_4_3","phase3_2_3"]
# }
#
# while traci.simulation.getMinExpectedNumber() > 0:
#     # 仿真开始前输入信号配时方案
#     if current_time == 0:
#         for phase_time, logic in third_logic_dict.items():
#             traci.trafficlight.setProgramLogic('light3', logic)
#     # 切换配时方案
#     if current_time in third_trigger_time_list:
#         traci.trafficlight.setProgram('light3', str(current_time))
#     # 开始仿真
#     traci.simulationStep()
#     current_time += 1
#
#     #计算周期开始和结束时间
#     time_interval_start = cycle_start_time
#     time_interval_end = cycle_start_time + signal_cycle
#
#     #开始仿真计算每秒交叉口等待车辆数
#     if time_interval_start <=  current_time <= time_interval_end:
#         for phase_name,lane_name in phase_to_lane.items():
#             for lane in lane_name:
#                 vehicle_list=traci.lane.getLastStepVehicleIDs(lane)
#
#                 for vehicle_id in vehicle_list:
#                     vehicle_type=traci.vehicle.getTypeID(vehicle_id)
#                     vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
#                     if vehicle_id not in vehicle_tracking and vehicle_speed<0.1:
#                         if vehicle_type=="car":
#                             car_count.append(vehicle_id)
#                             vehicle_tracking.append(vehicle_id)
#
#                         elif vehicle_type=="bus":
#                             bus_count.append(vehicle_id)
#                             vehicle_tracking.append(vehicle_id)
#
#     if current_time > time_interval_end:
#         #统计周期结束时的车辆数量
#         car_count_cycle=len(car_count)
#         bus_count_cycle=len(bus_count)
#         person_count_cycle=int(car_count_cycle*person_num_car+bus_count_cycle*person_num_bus)
#
#         print(f"Cycle {cycle_counter}: Cars={car_count_cycle}, Buses={bus_count_cycle}")
#         print(f"Cycle {cycle_counter}: delay_person={person_count_cycle}")
#
#         cycles.append(cycle_counter)
#         car_in_cycle.append(car_count_cycle)
#         bus_in_cycle.append(bus_count_cycle)
#         person_in_cycle.append(person_count_cycle)
#
#         car_count = []
#         bus_count = []
#
#         cycle_start_time = time_interval_end
#         cycle_counter += 1
#
# traci.close()
#
# #绘制折线图
# # plt.figure(figsize=(10,6))
# # plt.plot(cycles,car_in_cycle,label="Car")
# # plt.plot(cycles,bus_in_cycle,label="Bus")
# # plt.xlabel("Cycle")
# # plt.ylabel("Vehicle Count")
# # plt.title("Vehicle Count at the End of Each Cycle")
# # plt.legend()
# # plt.grid(True)
# # plt.show()
#
# # 绘制柱状图
# bar_width = 0.4  # 设置柱的宽度
# opacity = 0.8    # 设置透明度
#
# cycle_to_plot=cycles[:45]
# car_in_cycle_to_plot=car_in_cycle[:45]
# bus_in_cycle_to_plot=bus_in_cycle[:45]
# person_in_cycle_to_plot=person_in_cycle[:45]
# fig,ax1=plt.subplots(figsize=(10,6))
#
# ax1.bar(np.array(cycle_to_plot) - bar_width/2, car_in_cycle_to_plot, bar_width, label='Car', alpha=opacity)
# ax1.bar(np.array(cycle_to_plot) + bar_width/2, bus_in_cycle_to_plot, bar_width, label='Bus', alpha=opacity)
#
# #第一个纵坐标
# ax1.set_xlabel('Cycle')
# ax1.set_ylabel('Vehicle Count')
# ax1.tick_params('y')
# ax1.set_title('Vehicle Count at the End of Each Cycle')
#
# #第二个纵坐标
# ax2=ax1.twinx()
# ax2.plot(cycle_to_plot,person_in_cycle_to_plot, label='Person',color="red", marker="o")
# ax2.set_ylabel('Person Count')
# ax2.tick_params('y')
#
# # 调整 x 轴范围
# plt.xlim(min(cycle_to_plot) - 1, max(cycle_to_plot) + 1)
# # 显示图例
# ax1.legend(loc='upper left')
# ax2.legend(loc='upper right')
# # 显示图形
# plt.tight_layout()
# plt.show()

import csv
import traci
import pandas as pd
import sumolib
import random
import warnings
warnings.filterwarnings("ignore")
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.font_manager import FontProperties
font = FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf", size=18)

# 读取CSV文件并计算每个周期的绿灯时间总和
cycle_green_light = {}
with open('优化后的信号配时方案10%.csv', 'r') as csvfile:
    csvreader = csv.DictReader(csvfile)
    for row in csvreader:
        cycle = int(row['Cycle'])
        green_light = int(row['Green_Light_Time'])
        if cycle in cycle_green_light:
            cycle_green_light[cycle] += green_light
        else:
            cycle_green_light[cycle] = green_light

# 调整绿灯时间总和为90
for cycle, total_green_light in cycle_green_light.items():
    if total_green_light != 90:
        adjustment = 90 - total_green_light
        # 重新打开文件以进行迭代
        with open('优化后的信号配时方案10%.csv', 'r') as csvfile:
            csvreader = csv.DictReader(csvfile)
            # 创建一个临时列表，用于保存调整后的行数据
            adjusted_rows = []
            for row in csvreader:
                if int(row['Cycle']) == cycle and int(row['Phase']) == 3:
                    # 调整绿灯时间
                    row['Green_Light_Time'] = str(int(row['Green_Light_Time']) + adjustment)
                # 将行数据添加到临时列表中
                adjusted_rows.append(row)
        # 使用调整后的行数据覆盖原始文件的内容
        with open('优化后的信号配时方案10%.csv', 'w', newline='') as csvfile:
            fieldnames = ['Cycle', 'Intersection', 'Phase', 'Green_Light_Time']
            csvwriter = csv.DictWriter(csvfile, fieldnames=fieldnames)
            csvwriter.writeheader()
            csvwriter.writerows(adjusted_rows)


# 初始信号配时方案
signal_plan_before = pd.read_csv('初始信号配时方案1.csv')
before_trigger_time_list = signal_plan_before['Cycle'].unique().tolist()

# 连接sumo
sumo_binary = "C:/Program Files (x86)/Eclipse/Sumo/bin/sumo-gui.exe"
sumo_cfg_file = "bus_NormalIntersection10%.sumocfg"
traci.start([sumo_binary, "-c", sumo_cfg_file])

# 全局phase(三号交叉口)定义
phase_dict = {'1': 'GrrrrGGGGrrGrrrGGGGrr',
              '2': 'GrrrrGGrrGGGrrrGrrrGG',
              '3': 'GGGrrGGrrrrGGGrGrrrrr',
              '4': 'GrrGGGGrrrrGrrGGrrrrr'}

before_phase_duration_dict = signal_plan_before.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

before_logic_dict = {}

for phase_time, duration_dict in before_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))

    before_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#初始信号配时开始仿真
signal_cycle=90
person_num_bus=random.randint(35,45)
person_num_car=2
vehicle_tracking_before=[]
cycle_counter=1
current_time = 0
cycle_start_time=current_time

car_count_before = []
bus_count_before = []

cycles_before=[]
car_in_cycle_before=[]
bus_in_cycle_before=[]
person_in_cycle_before=[]

phase_to_lane={
    "1":["phase3_1_2","phase3_1_1","phase3_3_1","phase3_3_2","phase3_3_3"],
    "2":["phase3_1_3","phase3_3_4"],
    "3":["phase3_2_1","phase3_2_2","phase3_4_2","phase3_4_1"],
    "4":["phase3_4_3","phase3_2_3"]
}

while traci.simulation.getMinExpectedNumber() > 0:
    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in before_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)
    # 切换配时方案
    if current_time in before_trigger_time_list:
        traci.trafficlight.setProgram('light3', str(current_time))
    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_before and vehicle_speed<0.1:
                        if vehicle_type=="car":
                            car_count_before.append(vehicle_id)
                            vehicle_tracking_before.append(vehicle_id)

                        elif vehicle_type=="bus":
                            bus_count_before.append(vehicle_id)
                            vehicle_tracking_before.append(vehicle_id)

    if current_time > time_interval_end:
        #统计周期结束时的车辆数量
        car_count_cycle_before=len(car_count_before)
        bus_count_cycle_before=len(bus_count_before)
        person_count_cycle_before=int(car_count_cycle_before*person_num_car+bus_count_cycle_before*person_num_bus)

        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_before}, Buses={bus_count_cycle_before}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_before}")

        cycles_before.append(cycle_counter)
        car_in_cycle_before.append(car_count_cycle_before)
        bus_in_cycle_before.append(bus_count_cycle_before)
        person_in_cycle_before.append(person_count_cycle_before)

        car_count_before = []
        bus_count_before = []

        cycle_start_time = time_interval_end
        cycle_counter += 1

traci.close()


# 配时方案
signal_plan_after = pd.read_csv('优化后的信号配时方案10%.csv')
after_trigger_time_list = signal_plan_after['Cycle'].unique().tolist()

traci.start([sumo_binary, "-c", sumo_cfg_file])

after_phase_duration_dict = signal_plan_after.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

after_logic_dict = {}

for phase_time, duration_dict in after_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))

    after_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#优化信号配时方案开始仿真
vehicle_tracking_after=[]
cycle_counter=1
current_time = 0
cycle_start_time=current_time

car_count_after = []
bus_count_after = []

cycles_after=[]
car_in_cycle_after=[]
bus_in_cycle_after=[]
person_in_cycle_after=[]

while traci.simulation.getMinExpectedNumber() > 0:

    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in after_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案
    if current_time in after_trigger_time_list:
        traci.trafficlight.setProgram('light3', str(current_time))
    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_after and vehicle_speed<0.1:
                        if vehicle_type=="car":
                            car_count_after.append(vehicle_id)
                            vehicle_tracking_after.append(vehicle_id)

                        elif vehicle_type=="bus":
                            bus_count_after.append(vehicle_id)
                            vehicle_tracking_after.append(vehicle_id)

    if current_time > time_interval_end:
        #统计周期结束时的车辆数量
        car_count_cycle_after=len(car_count_after)
        bus_count_cycle_after=len(bus_count_after)
        person_count_cycle_after=int(car_count_cycle_after*person_num_car+bus_count_cycle_after*person_num_bus)

        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_after}, Buses={bus_count_cycle_after}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_after}")

        cycles_after.append(cycle_counter)
        car_in_cycle_after.append(car_count_cycle_after)
        bus_in_cycle_after.append(bus_count_cycle_after)
        person_in_cycle_after.append(person_count_cycle_after)

        car_count_after = []
        bus_count_after = []

        cycle_start_time = time_interval_end
        cycle_counter += 1

traci.close()
#
# cycles = cycles_before[:45]
# car_before = car_in_cycle_before[:45]
# bus_before = bus_in_cycle_before[:45]
# person_before = person_in_cycle_before[:45]
# car_after = car_in_cycle_after[:45]
# bus_after = bus_in_cycle_after[:45]
# person_after = person_in_cycle_after[:45]
#
# # 计算柱子的宽度
# bar_width = 0.35
# index = np.arange(len(cycles))
#
# # 绘制柱状图表示车辆数
# plt.figure(figsize=(12, 6))
# bar1 = plt.bar(index - bar_width/2, car_before, width=bar_width, label='优化前')
# bar3 = plt.bar(index + bar_width/2, car_after, width=bar_width, label='优化后')
#
# plt.xlabel('周期', fontproperties=font)
# plt.ylabel('延误社会车辆数', fontproperties=font)
# plt.title('公交车渗透率10%优化前后的延误社会车辆数', fontproperties=font)
# plt.xticks(index, cycles)
# plt.ylim(16,40)
# plt.legend(prop=font)
# plt.show()
#
# # 绘制柱状图表示车辆数
# bar2 = plt.bar(index - bar_width/2, bus_before,width=bar_width, label='优化前')
# bar4 = plt.bar(index + bar_width/2, bus_after, width=bar_width, label='优化后')
#
# plt.xlabel('周期', fontproperties=font)
# plt.ylabel('延误公交车辆数', fontproperties=font)
# plt.title('公交车渗透率10%优化前后的延误公交车辆数', fontproperties=font)
# plt.xticks(index, cycles)
# plt.ylim(0,11)
# plt.legend(prop=font)
# plt.show()
#
#
# # 绘制折线图表示人数
# plt.figure(figsize=(10, 6))
# plt.plot(cycles, person_before, label='优化前')
# plt.plot(cycles, person_after, label='优化后')
#
# plt.xlabel('周期', fontproperties=font)
# plt.ylabel('延误人数', fontproperties=font)
# plt.title('公交车渗透率10%优化前后延误人数', fontproperties=font)
# plt.legend(prop=font)
# plt.show()

# 数据
cycles = cycles_before[:45]
car_before = car_in_cycle_before[:45]
bus_before = bus_in_cycle_before[:45]
person_before = person_in_cycle_before[:45]
car_after = car_in_cycle_after[:45]
bus_after = bus_in_cycle_after[:45]
person_after = person_in_cycle_after[:45]

# 创建雷达图
labels = cycles

# 公交车数量
angles = np.linspace(0, 2*np.pi, len(labels), endpoint=False).tolist()
stats_bus_before = bus_before
stats_bus_after = bus_after

fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(polar=True))
ax.fill(angles, stats_bus_before, color='blue', alpha=0.25, label='公交专用道')
ax.fill(angles, stats_bus_after, color='green', alpha=0.25, label='公交优先信号')

ax.set_yticklabels(["{:.0f}".format(i) for i in ax.get_yticks()])  # 添加公交车数值刻度
ax.set_xticks(angles)
ax.set_xticklabels(labels)

plt.title('公交渗透率10%公交优先信号与公交专用道下公交车延误对比', fontproperties=font)
plt.legend(loc='upper right', prop=font)

plt.show()

# 社会车数量
stats_car_before = car_before
stats_car_after = car_after

fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(polar=True))
ax.fill(angles, stats_car_before, color='blue', alpha=0.25, label='公交专用道')
ax.fill(angles, stats_car_after, color='green', alpha=0.25, label='公交优先信号')

ax.set_yticklabels(["{:.0f}".format(i) for i in ax.get_yticks()])  # 添加社会车数值刻度
ax.set_xticks(angles)
ax.set_xticklabels(labels)

plt.title('公交渗透率10%公交优先信号与公交专用道下社会车延误对比', fontproperties=font)
plt.legend(loc='upper right', prop=font)
plt.show()

# 延误人数
stats_person_before = person_before
stats_person_after = person_after

fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(polar=True))
ax.fill(angles, stats_person_before, color='blue', alpha=0.25, label='公交专用道')
ax.fill(angles, stats_person_after, color='green', alpha=0.25, label='公交优先信号')

ax.set_yticklabels(["{:.0f}".format(i) for i in ax.get_yticks()])  # 添加延误人数数值刻度
ax.set_xticks(angles)
ax.set_xticklabels(labels)

plt.title('公交渗透率10%公交优先信号与公交专用道下延误人数对比', fontproperties=font)
plt.legend(loc='upper right', prop=font)

plt.show()