"""
计算优先相位公交推荐速度轨迹
"""
import csv
import random
import re


class TrafficLight:
    def __init__(self, traffic_data_file):
        self.phase = 0
        self.directions=[]
        self.traffic_data = self.read_traffic_data(traffic_data_file)
        self.signal_timings = []
        self.bus_length=13
        self.car_length=5
        self.car_gaps=3
        self.car_gaps_free=60
        self.bus_gaps=2
        self.bus_gaps_free=40
        self.bus_car_gaps=4
        self.bus_car_gaps_free=35
        self.car_person_capacity=1.5
        self.car_time_headway = 2
        self.bus_time_headway = 4
        self.bus_person_capacity=random.randint(25,35)
        # self.random_green_time = random.randint(20,25)
        self.cars_queue = {}
        self.buses_queue = {}
        self.current_cycle = 0
        self.cycle_time = 90  # 信号周期时间，假设为60秒
        self.mix_average_speed=8
        self.bus_max_speed=14
        self.car_max_speed=17
        self.bus_acc=1.5
        self.car_acc=2
        self.bus_dec=2.5
        #控制区长度
        self.control_length = 500

        #一个交叉口的信号配时
        self.green_light_times = {

            "intersection3": {
                "phase3_1": 40,
                "phase3_2": 10,
                "phase3_3": 20,
                "phase3_4": 10,
            }
        }

    def read_traffic_data(self, traffic_data_file):
        traffic_data = {}
        with open(traffic_data_file, mode='r', newline='', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                intersection = row['交叉口']
                phase = row['相位']
                car_arrival_rate = float(row['社会车到达率'])
                bus_arrival_rate = float(row['公交车到达率'])
                cycle_number = int(row['周期数'])

                if intersection not in traffic_data:
                    traffic_data[intersection] = {}
                if cycle_number not in traffic_data[intersection]:
                    traffic_data[intersection][cycle_number] = {}
                traffic_data[intersection][cycle_number][phase] = {
                    'car_arrival_rate': car_arrival_rate,
                    'bus_arrival_rate': bus_arrival_rate
                }
                if phase not in self.directions:
                    self.directions.append(phase)
        return traffic_data

    def calculate_arrivals(self, arrival_rate):
        arrivals = arrival_rate * self.cycle_time
        return arrivals

    def calculate_vehicle_arrives_leavings(self, intersection,cycle,phase):

        #计算k周期到达的车辆数
        car_arrival_rate = self.traffic_data[intersection][cycle][phase]['car_arrival_rate']
        bus_arrival_rate = self.traffic_data[intersection][cycle][phase]['bus_arrival_rate']
        cars_arrived = int(self.calculate_arrivals(car_arrival_rate))
        buses_arrived = int(self.calculate_arrivals(bus_arrival_rate))

        # 计算k周期排队长度
        queue_length = cars_arrived * (self.car_length + self.car_gaps)+ buses_arrived * (self.bus_length + self.bus_gaps)+(2*buses_arrived)*self.bus_car_gaps
        # 计算饱和密度
        saturation_density_bus = buses_arrived / queue_length
        saturation_density_car = cars_arrived / queue_length

        # 计算饱和流速
        bus_saturation_flow_rate = saturation_density_bus * self.mix_average_speed
        car_saturation_flow_rate = saturation_density_car * self.mix_average_speed

        # 计算离开的汽车数量，取整
        leaving_buss = min(int(bus_saturation_flow_rate * self.green_light_times[intersection][phase]),
                           buses_arrived + self.buses_queue.get((intersection, cycle, phase), 0))
        leaving_cars = min(int(car_saturation_flow_rate * self.green_light_times[intersection][phase]),
                           cars_arrived + self.cars_queue.get((intersection, cycle, phase), 0))
        return cars_arrived,buses_arrived,leaving_buss,leaving_cars,queue_length

    def vehicle_queue_num(self, intersection,cycle, phase):

        cars_arrived_k,buses_arrived_k,buses_leaved_k,cars_leaved_k,queue_length = self.calculate_vehicle_arrives_leavings(intersection,cycle,phase)
        # 更新本周期排队车辆数
        self.cars_queue[(intersection,cycle,phase)] = cars_arrived_k - cars_leaved_k
        print(self.cars_queue[(intersection,cycle,phase)])
        self.buses_queue[(intersection,cycle,phase)] = buses_arrived_k- buses_leaved_k
        print(self.buses_queue[(intersection,cycle,phase)])
        return cars_arrived_k,buses_arrived_k

    def extract_intersection_phase_number(self, phase):
        match = re.search(r'phase(\d+)_(\d+)', phase)
        return tuple(map(int, match.groups())) if match else (None, None)

    #计算交通波
    def traffic_wave(self,intersection,cycle,phase):
        car_arrive_k,bus_arrive_k = self.vehicle_queue_num(intersection,cycle,phase)
        #状态1(自由流的状态)
        Q1 = ((car_arrive_k + bus_arrive_k) / self.cycle_time) *3600 #(辆/h)
        K1 = 1000 / (self.car_gaps_free + self.car_length + self.bus_gaps_free + self.bus_length ) #(辆/km)

        #状态2（停车时候的状态）
        Q2 = 0
        K2 = 1000 / (self.car_gaps + self.car_length)

        # 状态3（车流启动时候的状态）
        Q3 = 3600 / (self.car_time_headway)
        K3 = K2 /2

        #停车波
        stop_wave_speed = abs((Q1 - Q2)/(K1 - K2))
        #启动波
        drive_wave_speed = abs((Q3 - Q2)/(K3 - K2))
        return stop_wave_speed,drive_wave_speed

    def speed_control(self,intersection,cycle,phase):
        #启动波与减速波
        stop_wave,drive_wave = self.traffic_wave(intersection,cycle,phase)
        #前方车辆排队长度
        bus_queue = self.buses_queue['intersection3', 1, 'phase3_1']
        car_queue = self.cars_queue['intersection3', 1, 'phase3_1']
        cars_arrived_k, buses_arrived_k, buses_leaved_k, cars_leaved_k, queue_length = self.calculate_vehicle_arrives_leavings(
            intersection, cycle, phase)

        #距离排队车辆尾部距离
        slow_length = self.control_length - (queue_length)

        #初始化 low_speed 和 high_speed
        low_speed = 4
        high_speed = self.bus_max_speed

        if queue_length != 0:
            #前方排队车辆消散时间
            drive_time_car = queue_length/ stop_wave
            #推荐的低速行驶速度
            low_speed = int(slow_length/(drive_time_car) + 1)
        else:
            # 推荐高速行驶速度
            if high_speed >= self.bus_max_speed:
                high_speed = self.bus_max_speed
            else:
                high_speed = int(slow_length / self.green_light_times["intersection3"]["phase3_1"] + 1)
        return low_speed,high_speed


    def run_traffic_light(self, total_cycles, output_file):
        green_light_times_data = {}  # 用于存储每个周期各个相位的绿灯信号配时信息

        with open(output_file, 'w', newline='') as file:
            fieldnames = ["Cycle", "Intersection", "Phase", "Low_speed","High_speed"]
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            writer.writeheader()

            for cycle in range(1, total_cycles + 1):
                green_light_times_cycle = {}  # 存储当前周期的绿灯信号配时信息

                for intersection in self.traffic_data:
                    for phase in self.traffic_data[intersection][cycle]:
                        if phase == "phase3_1":
                            intersection_num, phase_num = self.extract_intersection_phase_number(phase)
                            self.vehicle_queue_num(intersection, cycle, phase)

                            stop_wave_speed,drive_wave_speed = self.traffic_wave(intersection,cycle,phase)
                            print(f"停车波:{stop_wave_speed}")
                            print(f"启动波:{drive_wave_speed}")
                            low_speed,high_speed=self.speed_control(intersection,cycle,phase)


                            waiting_cars = self.cars_queue.get((intersection, cycle, phase))
                            waiting_buses = self.buses_queue.get((intersection, cycle, phase))
                            waiting_person = int(waiting_cars * self.car_person_capacity + waiting_buses * self.bus_person_capacity)

                            # 获取当前信号相位的配时信息
                            current_green_light_time = self.green_light_times.get(intersection, {}).get(phase, 0)
                            green_light_times_cycle[(intersection_num, phase_num)] = current_green_light_time

                            # 保存到csv文件中
                            writer.writerow({
                                "Cycle": cycle,
                                "Intersection": intersection_num,
                                "Phase": phase_num,
                                "Low_speed":low_speed,
                                "High_speed":high_speed
                            })

if __name__ == "__main__":

    traffic_light_simulation = TrafficLight("vehicle_arrive_rate.csv")
    total_cycles = 40
    traffic_light_simulation.run_traffic_light(total_cycles,"推荐速度.csv")
