

import traci
import pandas as pd
import sumolib
import random
import warnings
warnings.filterwarnings("ignore")
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.font_manager import FontProperties
font = FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf", size=18)


# 初始信号配时方案
signal_plan_before = pd.read_csv('初始信号配时方案1.csv')
before_trigger_time_list = signal_plan_before['Cycle'].unique().tolist()

# 连接sumo
sumo_binary = "C:/Program Files (x86)/Eclipse/Sumo/bin/sumo-gui.exe"
sumo_cfg_file = "NormalIntersection15%.sumocfg"
traci.start([sumo_binary, "-c", sumo_cfg_file])

# 全局phase(三号交叉口)定义
phase_dict = {'1': 'GrrrrGGGGrrGrrrGGGGrr',
              '2': 'GrrrrGGrrGGGrrrGrrrGG',
              '3': 'GGGrrGGrrrrGGGrGrrrrr',
              '4': 'GrrGGGGrrrrGrrGGrrrrr'}

before_phase_duration_dict = signal_plan_before.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

before_logic_dict = {}

for phase_time, duration_dict in before_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))

    before_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#初始信号配时开始仿真
signal_cycle=90
person_num_bus=random.randint(15,20)
person_num_car=1.5
vehicle_tracking_before=[]
cycle_counter=1
current_time = 0
cycle_start_time=current_time
cycle_track = []

car_count_before = []
bus_count_before = []

cycles_before=[]
car_in_cycle_before=[]
bus_in_cycle_before=[]
person_in_cycle_before=[]

phase_to_lane={
    "1":["phase3_1_2","phase3_1_1","phase3_3_1","phase3_3_2","phase3_3_3"],
    "2":["phase3_1_3","phase3_3_4"],
    "3":["phase3_2_1","phase3_2_2","phase3_4_2","phase3_4_1"],
    "4":["phase3_4_3","phase3_2_3"]
}

# while traci.simulation.getMinExpectedNumber() > 0:
while traci.simulation.getTime() <4060:
    cycle_counter = current_time // signal_cycle + 1
    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in before_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)
    # 切换配时方案
    if cycle_counter in before_trigger_time_list:
        if cycle_counter not in cycle_track:
            traci.trafficlight.setProgram('light3', str(cycle_counter))
    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_before and vehicle_speed<0.1:
                        if vehicle_type=="car":
                            car_count_before.append(vehicle_id)
                            vehicle_tracking_before.append(vehicle_id)

                        elif vehicle_type=="bus":
                            bus_count_before.append(vehicle_id)
                            vehicle_tracking_before.append(vehicle_id)

    if current_time > time_interval_end:
        #统计周期结束时的车辆数量
        car_count_cycle_before=len(car_count_before)
        bus_count_cycle_before=len(bus_count_before)
        person_count_cycle_before=int(car_count_cycle_before*person_num_car+bus_count_cycle_before*person_num_bus)

        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_before}, Buses={bus_count_cycle_before}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_before}")

        cycles_before.append(cycle_counter)
        car_in_cycle_before.append(car_count_cycle_before)
        bus_in_cycle_before.append(bus_count_cycle_before)
        person_in_cycle_before.append(person_count_cycle_before)

        car_count_before = []
        bus_count_before = []

        cycle_start_time = time_interval_end
        cycle_counter += 1

traci.close()


# 配时方案
signal_plan_after = pd.read_csv('优化后的信号配时方案15%.csv')
after_trigger_time_list = signal_plan_after['Cycle'].unique().tolist()

traci.start([sumo_binary, "-c", sumo_cfg_file])

after_phase_duration_dict = signal_plan_after.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

after_logic_dict = {}

for phase_time, duration_dict in after_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))

    after_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#优化信号配时方案开始仿真
vehicle_tracking_after=[]
cycle_counter=1
current_time = 0
cycle_start_time=current_time
track_cycle = []

car_count_after = []
bus_count_after = []

cycles_after=[]
car_in_cycle_after=[]
bus_in_cycle_after=[]
person_in_cycle_after=[]

while traci.simulation.getMinExpectedNumber() > 0:
# while traci.simulation.getTime() <4060:
    cycle_counter =current_time // signal_cycle + 1
    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in after_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案
    if cycle_counter in after_trigger_time_list:
        if cycle_counter not in track_cycle:
            traci.trafficlight.setProgram('light3', str(cycle_counter))
    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_after and vehicle_speed<0.1:
                        if vehicle_type=="car":
                            car_count_after.append(vehicle_id)
                            vehicle_tracking_after.append(vehicle_id)

                        elif vehicle_type=="bus":
                            bus_count_after.append(vehicle_id)
                            vehicle_tracking_after.append(vehicle_id)

    if current_time > time_interval_end:
        #统计周期结束时的车辆数量
        car_count_cycle_after=len(car_count_after)
        bus_count_cycle_after=len(bus_count_after)
        person_count_cycle_after=int(car_count_cycle_after*person_num_car+bus_count_cycle_after*person_num_bus)

        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_after}, Buses={bus_count_cycle_after}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_after}")

        cycles_after.append(cycle_counter)
        car_in_cycle_after.append(car_count_cycle_after)
        bus_in_cycle_after.append(bus_count_cycle_after)
        person_in_cycle_after.append(person_count_cycle_after)

        car_count_after = []
        bus_count_after = []

        cycle_start_time = time_interval_end
        cycle_counter += 1

traci.close()


# 数据
cycles = cycles_before[:45]
car_before = car_in_cycle_before[:45]
bus_before = bus_in_cycle_before[:45]
person_before = person_in_cycle_before[:45]
car_after = car_in_cycle_after[:45]
bus_after = bus_in_cycle_after[:45]
person_after = person_in_cycle_after[:45]

# 创建雷达图
labels = cycles

# 公交车数量
angles = np.linspace(0, 2*np.pi, len(labels), endpoint=False).tolist()
stats_bus_before = bus_before
stats_bus_after = bus_after

fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(polar=True))
ax.fill(angles, stats_bus_before, color='blue', alpha=0.25, label='优化前')
ax.fill(angles, stats_bus_after, color='green', alpha=0.25, label='优化后')

ax.set_yticklabels(["{:.0f}".format(i) for i in ax.get_yticks()])  # 添加公交车数值刻度
ax.set_xticks(angles)
ax.set_xticklabels(labels)

plt.title('公交渗透率15%下优化前后公交车延误对比', fontproperties=font)
plt.legend(loc='upper right', prop=font)

plt.show()

# 社会车数量
stats_car_before = car_before
stats_car_after = car_after

fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(polar=True))
ax.fill(angles, stats_car_before, color='blue', alpha=0.25, label='优化前')
ax.fill(angles, stats_car_after, color='green', alpha=0.25, label='优化后')

ax.set_yticklabels(["{:.0f}".format(i) for i in ax.get_yticks()])  # 添加社会车数值刻度
ax.set_xticks(angles)
ax.set_xticklabels(labels)

plt.title('公交渗透率15%下优化前后社会车延误对比', fontproperties=font)
plt.legend(loc='upper right', prop=font)

plt.show()

# 延误人数
stats_person_before = person_before
stats_person_after = person_after

fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(polar=True))
ax.fill(angles, stats_person_before, color='blue', alpha=0.25, label='优化前')
ax.fill(angles, stats_person_after, color='green', alpha=0.25, label='优化后')

ax.set_yticklabels(["{:.0f}".format(i) for i in ax.get_yticks()])  # 添加延误人数数值刻度
ax.set_xticks(angles)
ax.set_xticklabels(labels)

plt.title('公交渗透率15%下优化前后延误人数对比', fontproperties=font)
plt.legend(loc='upper right', prop=font)

plt.show()