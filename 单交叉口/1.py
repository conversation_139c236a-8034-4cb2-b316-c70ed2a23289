import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 读取三个CSV文件
data_no = pd.read_csv('初始信号方案下单交叉口延误.csv')
data_vehicle = pd.read_csv('公交优先信号下单交叉口延误.csv')
data_clxt = pd.read_csv('车路协同下单交叉口延误.csv')

num_vars = 11
# 提取第40到20个周期的数据
selected_data_intersection1_no = data_no.iloc[:num_vars, :]
selected_data_intersection1_vehicle = data_vehicle.iloc[:num_vars, :]
selected_data_intersection1_clxt = data_clxt.iloc[:num_vars, :]


# 合并三个交叉口同一周期的delay_bus数据
delay_bus_total_no = selected_data_intersection1_no['delay_bus_1'].values.astype(float) + selected_data_intersection1_no['delay_bus_2'].values.astype(float) + selected_data_intersection1_no['delay_bus_3'].values.astype(float)
delay_bus_total_vehicle = selected_data_intersection1_vehicle['delay_bus_1'].values.astype(float) + selected_data_intersection1_vehicle['delay_bus_2'].values.astype(float) + selected_data_intersection1_vehicle['delay_bus_3'].values.astype(float)
delay_bus_total_clxt = selected_data_intersection1_clxt['delay_bus_1'].values.astype(float) + selected_data_intersection1_clxt['delay_bus_2'].values.astype(float) + selected_data_intersection1_clxt['delay_bus_3'].values.astype(float)

# 构建雷达图
angles = np.linspace(0, 2 * np.pi, num_vars, endpoint=False).tolist()

fig, ax = plt.subplots(figsize=(6, 6), subplot_kw=dict(polar=True))
ax.set_theta_offset(np.pi / 2)
ax.set_theta_direction(-1)

plt.xticks(angles, range(num_vars))

# 绘制同一周期内三个交叉口delay_bus的总和的雷达图
ax.plot(angles, delay_bus_total_no, linewidth=1, linestyle='solid', label='Total Delay Bus-Initial signal timing', color='gray')
ax.plot(angles, delay_bus_total_vehicle, linewidth=1, linestyle='solid', label='Total Delay Bus-based on vehicle', color='blue')
ax.plot(angles, delay_bus_total_clxt, linewidth=1, linestyle='solid', label='Total Delay Bus-clxt signal timing', color='red')

ax.fill(angles, delay_bus_total_no, alpha=0.1, color='gray')
ax.fill(angles, delay_bus_total_vehicle, alpha=0.1, color='blue')
ax.fill(angles, delay_bus_total_clxt, alpha=0.1, color='red')

# 连接首尾以形成封闭图形
ax.plot([angles[0], angles[-1]], [delay_bus_total_no[0], delay_bus_total_no[-1]], linewidth=1, linestyle='solid', color='gray')
ax.plot([angles[0], angles[-1]], [delay_bus_total_vehicle[0], delay_bus_total_vehicle[-1]], linewidth=1, linestyle='solid', color='blue')
ax.plot([angles[0], angles[-1]], [delay_bus_total_clxt[0], delay_bus_total_clxt[-1]], linewidth=1, linestyle='solid', color='red')

plt.legend(loc='upper right')
plt.show()
