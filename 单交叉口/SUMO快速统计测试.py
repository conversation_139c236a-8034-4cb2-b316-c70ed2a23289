#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SUMO快速统计测试
无GUI模式快速测试交通流统计功能
"""

import os
import sys
import pandas as pd
from collections import defaultdict

# 检查SUMO环境
if 'SUMO_HOME' in os.environ:
    tools = os.path.join(os.environ['SUMO_HOME'], 'tools')
    sys.path.append(tools)
else:
    print("警告：未设置SUMO_HOME环境变量，尝试使用本地TraCI")
    try:
        import traci
    except ImportError:
        print("❌ 无法导入TraCI，请安装SUMO并设置环境变量")
        sys.exit(1)

try:
    import traci
    import sumolib
except ImportError as e:
    print(f"❌ 导入SUMO模块失败: {e}")
    sys.exit(1)

class QuickTrafficCounter:
    """快速交通流统计器"""
    
    def __init__(self, cycle_duration=90):
        self.cycle_duration = cycle_duration
        self.approach_edges = {
            '北': 'E41',
            '南': 'E1.22', 
            '东': 'E22',
            '西': 'E37'
        }
        self.stats = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
        self.vehicle_counted = set()
        
    def run_quick_test(self, max_time=360):  # 测试4个周期
        """运行快速测试"""
        print(f"🚀 启动SUMO快速测试 (最大时间: {max_time}秒)")
        
        try:
            # 启动SUMO（无GUI模式）
            sumo_cmd = ["sumo", "-c", "NormalIntersection.sumocfg", "--no-warnings"]
            traci.start(sumo_cmd)
            
            current_cycle = 1
            
            while traci.simulation.getMinExpectedNumber() > 0:
                current_time = traci.simulation.getTime()
                
                if current_time >= max_time:
                    break
                
                # 计算当前周期
                cycle = int(current_time // self.cycle_duration) + 1
                if cycle != current_cycle:
                    self.print_cycle_stats(current_cycle)
                    current_cycle = cycle
                
                # 统计车辆
                self.count_vehicles(cycle)
                
                # 执行仿真步
                traci.simulationStep()
            
            # 打印最后一个周期
            self.print_cycle_stats(current_cycle)
            
            traci.close()
            print("✅ 快速测试完成")
            
            return self.export_quick_results()
            
        except Exception as e:
            print(f"❌ 快速测试失败: {e}")
            try:
                traci.close()
            except:
                pass
            return None
    
    def count_vehicles(self, cycle):
        """统计车辆"""
        try:
            vehicle_ids = traci.vehicle.getIDList()
            
            for vid in vehicle_ids:
                if vid in self.vehicle_counted:
                    continue
                
                try:
                    edge = traci.vehicle.getRoadID(vid)
                    vtype = traci.vehicle.getTypeID(vid)
                    
                    # 判断方向
                    direction = None
                    for dir_name, edge_id in self.approach_edges.items():
                        if edge == edge_id:
                            direction = dir_name
                            break
                    
                    if direction:
                        vehicle_class = '公交车' if vtype == 'bus' else '社会车辆'
                        self.stats[cycle][direction][vehicle_class] += 1
                        self.vehicle_counted.add(vid)
                        
                except:
                    continue
                    
        except Exception as e:
            pass
    
    def print_cycle_stats(self, cycle):
        """打印周期统计"""
        print(f"\n📊 周期 {cycle} 统计:")
        print("-" * 40)
        
        total = 0
        for direction in ['北', '南', '东', '西']:
            cars = self.stats[cycle][direction]['社会车辆']
            buses = self.stats[cycle][direction]['公交车']
            dir_total = cars + buses
            total += dir_total
            print(f"{direction}进口: 车{cars:2d} 公交{buses:2d} 小计{dir_total:2d}")
        
        print(f"总计: {total} 辆")
    
    def export_quick_results(self):
        """导出快速测试结果"""
        data = []
        for cycle in sorted(self.stats.keys()):
            row = {'周期': cycle}
            for direction in ['北', '南', '东', '西']:
                row[f'{direction}_社会车辆'] = self.stats[cycle][direction]['社会车辆']
                row[f'{direction}_公交车'] = self.stats[cycle][direction]['公交车']
            data.append(row)
        
        if data:
            df = pd.DataFrame(data)
            output_file = "SUMO快速测试结果.csv"
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"📁 结果已保存: {output_file}")
            print("\n📋 测试结果预览:")
            print(df.to_string(index=False))
            return df
        
        return None

def test_sumo_environment():
    """测试SUMO环境"""
    print("🔧 测试SUMO环境...")
    
    # 检查配置文件
    config_file = "NormalIntersection.sumocfg"
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    # 检查网络文件
    net_file = "NormalIntersection.net.xml"
    if not os.path.exists(net_file):
        print(f"❌ 网络文件不存在: {net_file}")
        return False
    
    # 检查路径文件
    route_file = "一个交叉口车流.xml"
    if not os.path.exists(route_file):
        print(f"❌ 路径文件不存在: {route_file}")
        return False
    
    print("✅ SUMO环境检查通过")
    return True

def main():
    """主函数"""
    print("SUMO快速交通流统计测试")
    print("=" * 50)
    
    # 测试环境
    if not test_sumo_environment():
        return
    
    # 运行快速测试
    counter = QuickTrafficCounter(cycle_duration=90)
    result = counter.run_quick_test(max_time=360)  # 测试4个周期
    
    if result is not None:
        print("\n🎉 快速测试成功完成！")
        print("💡 如需完整仿真，请运行 'SUMO实时交通流统计.py'")
    else:
        print("\n❌ 快速测试失败")

if __name__ == "__main__":
    main()
