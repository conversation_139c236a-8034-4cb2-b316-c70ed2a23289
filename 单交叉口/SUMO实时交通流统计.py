#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SUMO实时交通流统计器
启动SUMO仿真并实时统计每个周期内四个进口方向的社会车和公交车数量
作者：AI助手
日期：2025-08-04
"""

import os
import sys
import time
import pandas as pd
import numpy as np
from collections import defaultdict
from typing import Dict, List, Tuple

# 检查SUMO环境
if 'SUMO_HOME' in os.environ:
    tools = os.path.join(os.environ['SUMO_HOME'], 'tools')
    sys.path.append(tools)
else:
    sys.exit("请设置环境变量 'SUMO_HOME'")

import traci
import sumolib

class SUMORealTimeTrafficCounter:
    """SUMO实时交通流统计器"""
    
    def __init__(self, sumo_config_file: str, cycle_duration: int = 90):
        """
        初始化统计器
        
        Args:
            sumo_config_file (str): SUMO配置文件路径
            cycle_duration (int): 周期时长（秒），默认90秒
        """
        self.sumo_config_file = sumo_config_file
        self.cycle_duration = cycle_duration
        self.current_cycle = 1
        self.simulation_step = 0
        
        # 进口道映射（基于XML文件中的from字段）
        self.approach_edges = {
            '北': 'E41',    # 北进口道
            '南': 'E1.22',  # 南进口道  
            '东': 'E22',    # 东进口道
            '西': 'E37'     # 西进口道
        }
        
        # 统计数据存储
        self.cycle_stats = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
        self.vehicle_records = {}  # 记录已统计的车辆
        self.real_time_data = []
        
        print(f"初始化SUMO实时交通流统计器")
        print(f"配置文件: {sumo_config_file}")
        print(f"周期时长: {cycle_duration}秒")
        print(f"进口道映射: {self.approach_edges}")
    
    def start_sumo_simulation(self, gui_mode: bool = True):
        """
        启动SUMO仿真
        
        Args:
            gui_mode (bool): 是否使用GUI模式
        """
        try:
            # 构建SUMO命令
            if gui_mode:
                sumo_cmd = ["sumo-gui", "-c", self.sumo_config_file]
            else:
                sumo_cmd = ["sumo", "-c", self.sumo_config_file]
            
            # 启动TraCI连接
            traci.start(sumo_cmd)
            print(f"✅ SUMO仿真已启动 ({'GUI模式' if gui_mode else '命令行模式'})")
            
            return True
            
        except Exception as e:
            print(f"❌ 启动SUMO仿真失败: {e}")
            return False
    
    def get_vehicle_info(self, vehicle_id: str) -> Tuple[str, str, str]:
        """
        获取车辆信息
        
        Args:
            vehicle_id (str): 车辆ID
            
        Returns:
            Tuple[str, str, str]: (车辆类型, 当前道路, 进口方向)
        """
        try:
            # 获取车辆类型
            vehicle_type = traci.vehicle.getTypeID(vehicle_id)
            vehicle_class = '公交车' if vehicle_type == 'bus' else '社会车辆'
            
            # 获取当前道路
            current_edge = traci.vehicle.getRoadID(vehicle_id)
            
            # 判断进口方向
            approach_direction = None
            for direction, edge_id in self.approach_edges.items():
                if current_edge == edge_id:
                    approach_direction = direction
                    break
            
            return vehicle_class, current_edge, approach_direction
            
        except Exception as e:
            return None, None, None
    
    def count_vehicles_in_cycle(self):
        """统计当前周期内的车辆"""
        try:
            # 获取所有车辆ID
            vehicle_ids = traci.vehicle.getIDList()
            
            for vehicle_id in vehicle_ids:
                # 避免重复统计同一车辆
                if vehicle_id in self.vehicle_records:
                    continue
                
                vehicle_class, current_edge, approach_direction = self.get_vehicle_info(vehicle_id)
                
                # 只统计在进口道上的车辆
                if approach_direction and current_edge in self.approach_edges.values():
                    self.cycle_stats[self.current_cycle][approach_direction][vehicle_class] += 1
                    self.vehicle_records[vehicle_id] = {
                        'cycle': self.current_cycle,
                        'direction': approach_direction,
                        'type': vehicle_class,
                        'edge': current_edge
                    }
                    
                    print(f"🚗 周期{self.current_cycle}: {approach_direction}进口 +1 {vehicle_class} (车辆ID: {vehicle_id})")
        
        except Exception as e:
            print(f"⚠️ 统计车辆时出错: {e}")
    
    def print_cycle_summary(self):
        """打印当前周期汇总"""
        print(f"\n{'='*60}")
        print(f"📊 周期 {self.current_cycle} 统计汇总 (时间: {(self.current_cycle-1)*self.cycle_duration}-{self.current_cycle*self.cycle_duration}秒)")
        print(f"{'='*60}")
        
        if self.current_cycle in self.cycle_stats:
            cycle_data = self.cycle_stats[self.current_cycle]
            
            total_vehicles = 0
            for direction in ['北', '南', '东', '西']:
                if direction in cycle_data:
                    cars = cycle_data[direction].get('社会车辆', 0)
                    buses = cycle_data[direction].get('公交车', 0)
                    direction_total = cars + buses
                    total_vehicles += direction_total
                    
                    print(f"{direction}进口: 社会车辆 {cars} 辆, 公交车 {buses} 辆, 小计 {direction_total} 辆")
                else:
                    print(f"{direction}进口: 社会车辆 0 辆, 公交车 0 辆, 小计 0 辆")
            
            print(f"周期总计: {total_vehicles} 辆")
        else:
            print("本周期暂无车辆统计数据")
        
        print(f"{'='*60}\n")
    
    def save_real_time_data(self):
        """保存当前周期的实时数据"""
        cycle_data = {
            'cycle': self.current_cycle,
            'start_time': (self.current_cycle - 1) * self.cycle_duration,
            'end_time': self.current_cycle * self.cycle_duration
        }
        
        # 添加各方向各类型车辆数据
        for direction in ['北', '南', '东', '西']:
            for vehicle_type in ['社会车辆', '公交车']:
                count = self.cycle_stats[self.current_cycle][direction][vehicle_type]
                cycle_data[f'{direction}_{vehicle_type}'] = count
        
        self.real_time_data.append(cycle_data)
    
    def run_simulation(self, max_cycles: int = None):
        """
        运行仿真并实时统计
        
        Args:
            max_cycles (int): 最大周期数，None表示运行到仿真结束
        """
        print(f"🚀 开始运行SUMO仿真...")
        print(f"实时统计周期: {self.cycle_duration}秒")
        if max_cycles:
            print(f"最大周期数: {max_cycles}")
        print(f"{'='*60}")
        
        try:
            while traci.simulation.getMinExpectedNumber() > 0:
                # 执行一个仿真步
                traci.simulationStep()
                self.simulation_step += 1
                
                # 统计当前步的车辆
                self.count_vehicles_in_cycle()
                
                # 检查是否完成一个周期
                if self.simulation_step % self.cycle_duration == 0:
                    # 打印周期汇总
                    self.print_cycle_summary()
                    
                    # 保存实时数据
                    self.save_real_time_data()
                    
                    # 进入下一个周期
                    self.current_cycle += 1
                    
                    # 检查是否达到最大周期数
                    if max_cycles and self.current_cycle > max_cycles:
                        print(f"✅ 已达到最大周期数 {max_cycles}，停止仿真")
                        break
                
                # 每10步显示一次进度
                if self.simulation_step % 10 == 0:
                    current_time = self.simulation_step
                    cycle_progress = (current_time % self.cycle_duration) / self.cycle_duration * 100
                    print(f"⏱️ 仿真时间: {current_time}秒, 周期{self.current_cycle}进度: {cycle_progress:.1f}%")
        
        except Exception as e:
            print(f"❌ 仿真运行出错: {e}")
        
        finally:
            # 处理最后一个未完成的周期
            if self.simulation_step % self.cycle_duration != 0:
                print(f"\n⚠️ 最后一个周期未完成，仿真在第{self.simulation_step}秒结束")
                self.print_cycle_summary()
                self.save_real_time_data()
            
            # 关闭TraCI连接
            traci.close()
            print(f"✅ SUMO仿真已结束")
    
    def export_results(self, output_file: str = "SUMO实时交通流统计结果.csv"):
        """导出统计结果"""
        if not self.real_time_data:
            print("❌ 没有可导出的数据")
            return
        
        try:
            df = pd.DataFrame(self.real_time_data)
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"📁 统计结果已导出到: {output_file}")
            
            # 显示汇总统计
            print(f"\n📊 总体统计汇总:")
            print(f"总周期数: {len(self.real_time_data)}")
            
            # 计算各方向总计
            for direction in ['北', '南', '东', '西']:
                cars_col = f'{direction}_社会车辆'
                buses_col = f'{direction}_公交车'
                if cars_col in df.columns and buses_col in df.columns:
                    total_cars = df[cars_col].sum()
                    total_buses = df[buses_col].sum()
                    print(f"{direction}进口总计: 社会车辆 {total_cars} 辆, 公交车 {total_buses} 辆")
            
            return df
            
        except Exception as e:
            print(f"❌ 导出结果时出错: {e}")
            return None


def main():
    """主函数"""
    print("SUMO实时交通流统计器")
    print("="*60)
    
    # 配置参数
    sumo_config = "NormalIntersection.sumocfg"
    cycle_duration = 90  # 90秒周期
    max_cycles = 40      # 最多统计10个周期（可根据需要调整）
    
    # 检查配置文件是否存在
    if not os.path.exists(sumo_config):
        print(f"❌ 配置文件不存在: {sumo_config}")
        return
    
    # 创建统计器
    counter = SUMORealTimeTrafficCounter(sumo_config, cycle_duration)
    
    # 启动仿真
    if counter.start_sumo_simulation(gui_mode=True):  # 设置为False可使用命令行模式
        # 运行仿真并统计
        counter.run_simulation(max_cycles=max_cycles)
        
        # 导出结果
        df = counter.export_results()
        
        print(f"\n🎉 实时交通流统计完成！")
    else:
        print(f"❌ 无法启动SUMO仿真")


if __name__ == "__main__":
    main()
