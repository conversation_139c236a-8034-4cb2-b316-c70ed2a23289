#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SUMO交通流分析器 - 简化使用示例
专门用于分析交叉口四个进口方向在指定周期内的交通流统计
"""

from 交通流分析器_修正版 import SUMOTrafficAnalyzer

def analyze_specific_cycles(xml_file: str, target_cycles: list, cycle_duration: int = 90):
    """
    分析指定周期的交通流数据
    
    Args:
        xml_file (str): XML文件路径
        target_cycles (list): 要分析的周期列表，例如 [3, 4]
        cycle_duration (int): 周期时长（秒），默认90秒
    """
    print(f"开始分析交通流数据")
    print(f"文件: {xml_file}")
    print(f"目标周期: {target_cycles}")
    print(f"周期时长: {cycle_duration}秒")
    print("="*80)
    
    # 创建分析器
    analyzer = SUMOTrafficAnalyzer(xml_file, cycle_duration)
    
    # 解析XML文件
    if not analyzer.parse_xml_file():
        print("❌ XML文件解析失败")
        return None
    
    # 生成分析报告
    df, processed_data = analyzer.generate_detailed_report(target_cycles)
    
    # 返回结果用于进一步处理
    return df, processed_data

def quick_summary(df):
    """快速汇总统计信息"""
    if df is None or df.empty:
        print("❌ 没有可用的数据进行汇总")
        return
    
    print("\n" + "="*60)
    print("📊 快速汇总统计")
    print("="*60)
    
    # 总车辆数
    total_vehicles = df['总计'].sum()
    print(f"总车辆数: {total_vehicles:.1f} 辆")
    
    # 各方向车辆数排名
    direction_totals = df.groupby('进口方向')['总计'].sum().sort_values(ascending=False)
    print(f"\n各方向车辆数排名:")
    for i, (direction, count) in enumerate(direction_totals.items(), 1):
        print(f"  {i}. {direction}进口: {count:.1f} 辆")
    
    # 车辆类型比例
    vehicle_totals = df.groupby('车辆类型')['总计'].sum()
    car_ratio = vehicle_totals.get('社会车辆', 0) / total_vehicles * 100
    bus_ratio = vehicle_totals.get('公交车', 0) / total_vehicles * 100
    print(f"\n车辆类型比例:")
    print(f"  社会车辆: {car_ratio:.1f}%")
    print(f"  公交车: {bus_ratio:.1f}%")

def main():
    """主函数 - 演示不同的使用场景"""
    
    # 场景1：分析第3-4周期（您的原始需求）
    print("🚗 场景1：分析第3-4周期")
    df1, data1 = analyze_specific_cycles('一个交叉口车流.xml', [3, 4])
    quick_summary(df1)
    
    # 场景2：分析单个周期
    print("\n\n🚗 场景2：分析第10周期")
    df2, data2 = analyze_specific_cycles('一个交叉口车流.xml', [10])
    quick_summary(df2)
    
    # 场景3：分析更多周期
    print("\n\n🚗 场景3：分析第5-8周期")
    df3, data3 = analyze_specific_cycles('一个交叉口车流.xml', [5, 6, 7, 8])
    quick_summary(df3)
    
    # 场景4：使用不同的周期时长
    print("\n\n🚗 场景4：使用120秒周期时长分析第2-3周期")
    df4, data4 = analyze_specific_cycles('一个交叉口车流.xml', [2, 3], cycle_duration=120)
    quick_summary(df4)
    
    print("\n" + "="*80)
    print("✅ 所有分析完成！")
    print("📁 生成的文件:")
    print("   - 交通流分析结果_周期*.csv (详细统计数据)")
    print("💡 提示：您可以修改 target_cycles 参数来分析任意周期")

if __name__ == "__main__":
    main()
