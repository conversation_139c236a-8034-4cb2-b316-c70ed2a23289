import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
font = FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf", size=10)

#画图----整个交叉口
# 读取三个CSV文件
data_no = pd.read_csv('初始信号方案下单交叉口延误.csv')
data_vehicle = pd.read_csv('公交优先信号下单交叉口延误.csv')
data_clxt = pd.read_csv('车路协同下单交叉口延误.csv')

num_vars = 40
# 提取第40到20个周期的数据
selected_data_intersection1_no = data_no.iloc[:num_vars, :]
selected_data_intersection1_vehicle = data_vehicle.iloc[:num_vars, :]
selected_data_intersection1_clxt = data_clxt.iloc[:num_vars, :]

# 合并三个交叉口同一周期的delay_bus数据
delay_bus_total_no = (selected_data_intersection1_no['delay_bus_1'].values.astype(float) + selected_data_intersection1_no['delay_bus_2'].values.astype(float)
                      + selected_data_intersection1_no['delay_bus_3'].values.astype(float)+ selected_data_intersection1_no['delay_bus_4'].values.astype(float))
delay_bus_total_vehicle = (selected_data_intersection1_vehicle['delay_bus_1'].values.astype(float) + selected_data_intersection1_vehicle['delay_bus_2'].values.astype(float)
                           + selected_data_intersection1_vehicle['delay_bus_3'].values.astype(float)+ selected_data_intersection1_vehicle['delay_bus_4'].values.astype(float))
delay_bus_total_clxt = (selected_data_intersection1_clxt['delay_bus_1'].values.astype(float) + selected_data_intersection1_clxt['delay_bus_2'].values.astype(float)
                        + selected_data_intersection1_clxt['delay_bus_3'].values.astype(float)+ selected_data_intersection1_clxt['delay_bus_4'].values.astype(float))

# 构建雷达图
angles = np.linspace(0, 2 * np.pi, num_vars, endpoint=False).tolist()

fig, ax = plt.subplots(figsize=(6, 6), subplot_kw=dict(polar=True))
ax.set_theta_offset(np.pi / 2)
ax.set_theta_direction(-1)

plt.xticks(angles, range(num_vars))

# 绘制同一周期内三个交叉口delay_bus的总和的雷达图
ax.plot(angles, delay_bus_total_no, linewidth=1, linestyle='solid', label='Total Delay Bus-Initial signal timing', color='gray')
ax.plot(angles, delay_bus_total_vehicle, linewidth=1, linestyle='solid', label='Total Delay Bus-based on vehicle', color='blue')
ax.plot(angles, delay_bus_total_clxt, linewidth=1, linestyle='solid', label='Total Delay Bus-clxt signal timing', color='red')

ax.fill(angles, delay_bus_total_no, alpha=0.1, color='gray')
ax.fill(angles, delay_bus_total_vehicle, alpha=0.1, color='blue')
ax.fill(angles, delay_bus_total_clxt, alpha=0.1, color='red')

# 连接首尾以形成封闭图形
ax.plot([angles[0], angles[-1]], [delay_bus_total_no[0], delay_bus_total_no[-1]], linewidth=1, linestyle='solid', color='gray')
ax.plot([angles[0], angles[-1]], [delay_bus_total_vehicle[0], delay_bus_total_vehicle[-1]], linewidth=1, linestyle='solid', color='blue')
ax.plot([angles[0], angles[-1]], [delay_bus_total_clxt[0], delay_bus_total_clxt[-1]], linewidth=1, linestyle='solid', color='red')

plt.legend(loc='upper right')
plt.show()

#画图----四相位分开
# angles = np.linspace(0, 2*np.pi, 4, endpoint=False).tolist()
# labels = ['相位1', '相位2', '相位3', '相位4']
# target_cycle = 9
#
# file_path_after3 = "车路协同下单交叉口延误.csv"
# df = pd.read_csv(file_path_after3)
# filtered_df_after_3 = df[df["cycle"]== target_cycle]
# #车路协同下的延误（提取）--社会车
# car_in_cycle_after3_1 =filtered_df_after_3['delay_car_1'].values[0]
# car_in_cycle_after3_2 =filtered_df_after_3['delay_car_2'].values[0]
# car_in_cycle_after3_3 =filtered_df_after_3['delay_car_3'].values[0]
# car_in_cycle_after3_4 =filtered_df_after_3['delay_car_4'].values[0]
# #车路协同下的延误（提取）--公交车
# bus_in_cycle_after3_1 = filtered_df_after_3['delay_bus_1'].values[0]
# bus_in_cycle_after3_2 = filtered_df_after_3['delay_bus_2'].values[0]
# bus_in_cycle_after3_3 = filtered_df_after_3['delay_bus_3'].values[0]
# bus_in_cycle_after3_4 = filtered_df_after_3['delay_bus_4'].values[0]
#
# #车路协同下的延误（提取）--乘客
# person_in_cycle_after3_1 = filtered_df_after_3['delay_person_1'].values[0]
# person_in_cycle_after3_2 = filtered_df_after_3['delay_person_2'].values[0]
# person_in_cycle_after3_3 = filtered_df_after_3['delay_person_3'].values[0]
# person_in_cycle_after3_4 = filtered_df_after_3['delay_person_4'].values[0]
#
# file_path_after1 = "公交优先信号下单交叉口延误.csv"
# df = pd.read_csv(file_path_after1)
# filtered_df_after_1 = df[df["cycle"]== target_cycle]
# #信号控制下的延误（提取）--社会车
# car_in_cycle_after1_1 = filtered_df_after_1["delay_car_1"].values[0]
# car_in_cycle_after1_2 = filtered_df_after_1["delay_car_2"].values[0]
# car_in_cycle_after1_3 = filtered_df_after_1["delay_car_3"].values[0]
# car_in_cycle_after1_4 = filtered_df_after_1["delay_car_4"].values[0]
# #信号控制下的延误（提取）--公交车
# bus_in_cycle_after1_1 = filtered_df_after_1["delay_bus_1"].values[0]
# bus_in_cycle_after1_2 = filtered_df_after_1["delay_bus_2"].values[0]
# bus_in_cycle_after1_3 = filtered_df_after_1["delay_bus_3"].values[0]
# bus_in_cycle_after1_4 = filtered_df_after_1["delay_bus_4"].values[0]
#
# #信号控制下的延误（提取）--乘客
# person_in_cycle_after1_1 = filtered_df_after_1["delay_person_1"].values[0]
# person_in_cycle_after1_2 = filtered_df_after_1["delay_person_2"].values[0]
# person_in_cycle_after1_3 = filtered_df_after_1["delay_person_3"].values[0]
# person_in_cycle_after1_4 = filtered_df_after_1["delay_person_4"].values[0]
#
# file_path_after2 = "速度控制下单交叉口延误.csv"
# df = pd.read_csv(file_path_after2)
# filtered_df_after_2 = df[df["cycle"]== target_cycle]
# #速度控制下的延误（提取）--社会车
# car_in_cycle_after2_1 = filtered_df_after_2["delay_car_1"].values[0]
# car_in_cycle_after2_2 = filtered_df_after_2["delay_car_2"].values[0]
# car_in_cycle_after2_3 = filtered_df_after_2["delay_car_3"].values[0]
# car_in_cycle_after2_4 = filtered_df_after_2["delay_car_4"].values[0]
# #速度控制下的延误（提取）--公交车
# bus_in_cycle_after2_1 = filtered_df_after_2["delay_bus_1"].values[0]
# bus_in_cycle_after2_2 = filtered_df_after_2["delay_bus_2"].values[0]
# bus_in_cycle_after2_3 = filtered_df_after_2["delay_bus_3"].values[0]
# bus_in_cycle_after2_4 = filtered_df_after_2["delay_bus_4"].values[0]
#
# #速度控制下的延误（提取）--公交车
# person_in_cycle_after2_1 = filtered_df_after_2["delay_person_1"].values[0]
# person_in_cycle_after2_2 = filtered_df_after_2["delay_person_2"].values[0]
# person_in_cycle_after2_3 = filtered_df_after_2["delay_person_3"].values[0]
# person_in_cycle_after2_4 = filtered_df_after_2["delay_person_4"].values[0]
#
# file_path_berore = "初始信号方案下单交叉口延误.csv"
# df = pd.read_csv(file_path_berore)
# filtered_df_berore = df[df["cycle"]== target_cycle]
# #初始信号下的延误（提取）--社会车
# car_in_cycle_before_1 = filtered_df_berore["delay_car_1"].values[0]
# car_in_cycle_before_2 = filtered_df_berore["delay_car_2"].values[0]
# car_in_cycle_before_3 = filtered_df_berore["delay_car_3"].values[0]
# car_in_cycle_before_4 = filtered_df_berore["delay_car_4"].values[0]
# #初始信号下的延误（提取）--公交车
# bus_in_cycle_before_1 = filtered_df_berore["delay_bus_1"].values[0]
# bus_in_cycle_before_2 = filtered_df_berore["delay_bus_2"].values[0]
# bus_in_cycle_before_3 = filtered_df_berore["delay_bus_3"].values[0]
# bus_in_cycle_before_4 = filtered_df_berore["delay_bus_4"].values[0]
#
# #初始信号下的延误（提取）--乘客
# person_in_cycle_before_1 = filtered_df_berore["delay_person_1"].values[0]
# person_in_cycle_before_2 = filtered_df_berore["delay_person_2"].values[0]
# person_in_cycle_before_3 = filtered_df_berore["delay_person_3"].values[0]
# person_in_cycle_before_4 = filtered_df_berore["delay_person_4"].values[0]
#
# #车路协同
# stats_car_after3 = [car_in_cycle_after3_1, car_in_cycle_after3_2, car_in_cycle_after3_3, car_in_cycle_after3_4]
# stats_bus_after3 = [bus_in_cycle_after3_1, bus_in_cycle_after3_2, bus_in_cycle_after3_3, bus_in_cycle_after3_4]
# stats_person_after3 = [person_in_cycle_after3_1, person_in_cycle_after3_2, person_in_cycle_after3_3, person_in_cycle_after3_4]
# #信号控制
# stats_car_after1 = [car_in_cycle_after1_1, car_in_cycle_after1_2, car_in_cycle_after1_3, car_in_cycle_after1_4]
# stats_bus_after1 = [bus_in_cycle_after1_1, bus_in_cycle_after1_2, bus_in_cycle_after1_3, bus_in_cycle_after1_4]
# stats_person_after1 = [person_in_cycle_after1_1, person_in_cycle_after1_2, person_in_cycle_after1_3, person_in_cycle_after1_4]
# #速度控制
# stats_car_after2 = [car_in_cycle_after2_1, car_in_cycle_after2_2, car_in_cycle_after2_3, car_in_cycle_after2_4]
# stats_bus_after2 = [bus_in_cycle_after2_1, bus_in_cycle_after2_2, bus_in_cycle_after2_3, bus_in_cycle_after2_4]
# stats_person_after2 = [person_in_cycle_after2_1, person_in_cycle_after2_2,person_in_cycle_after2_3, person_in_cycle_after2_4]
# #初始信号
# stats_car_before = [car_in_cycle_before_1,car_in_cycle_before_2,car_in_cycle_before_3,car_in_cycle_before_4]
# stats_bus_before = [bus_in_cycle_before_1,bus_in_cycle_before_2,bus_in_cycle_before_3,bus_in_cycle_before_4]
# stats_person_before = [person_in_cycle_before_1,person_in_cycle_before_2,person_in_cycle_before_3,person_in_cycle_before_4]
#
#
# plt.rcParams['font.family'] = 'Times New Roman'
# plt.rcParams['font.size'] = 10
# fig, axs = plt.subplots(1, 3, figsize=(18, 6), subplot_kw=dict(polar=True))
#
# axs[0].fill(angles, stats_car_before, color='gray', alpha=0.25, label='无控制')
# axs[0].fill(angles, stats_car_after2, color='blue', alpha=0.25, label='速度控制')
# axs[0].fill(angles, stats_car_after1, color='green', alpha=0.25, label='信号控制')
# axs[0].fill(angles, stats_car_after3, color='red', alpha=0.25, label='车路协同控制')
# axs[0].set_yticklabels([])
# axs[0].set_xticks(angles)
# axs[0].set_xticklabels(labels, fontproperties=font)
# axs[0].set_title("延误社会车(a)", fontproperties=font)
#
# axs[1].fill(angles, stats_bus_before, color='gray', alpha=0.25, label='无控制')
# axs[1].fill(angles, stats_bus_after2, color='blue', alpha=0.25, label='速度控制')
# axs[1].fill(angles, stats_bus_after1, color='green', alpha=0.25, label='信号控制')
# axs[1].fill(angles, stats_bus_after3, color='red', alpha=0.25, label='车路协同控制')
# axs[1].set_yticklabels([])
# axs[1].set_xticks(angles)
# axs[1].set_xticklabels(labels, fontproperties=font)
# axs[1].set_title("延误公交车(b)", fontproperties=font)
#
# axs[2].fill(angles, stats_person_before, color='gray', alpha=0.25, label='无控制')
# axs[2].fill(angles, stats_person_after2, color='blue', alpha=0.25, label='速度控制')
# axs[2].fill(angles, stats_person_after1, color='green', alpha=0.25, label='信号控制')
# axs[2].fill(angles, stats_person_after3, color='red', alpha=0.25, label='车路协同控制')
# axs[2].set_yticklabels([])
# axs[2].set_xticks(angles)
# axs[2].set_xticklabels(labels, fontproperties=font)
# axs[2].set_title("延误人数(c)", fontproperties=font)
#
# plt.legend(loc='upper right', prop=font)
# plt.tight_layout(rect=[0, 0.05, 1, 0.95])
#
# plt.show()
