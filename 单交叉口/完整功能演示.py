#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SUMO交通流统计系统 - 完整功能演示
展示所有功能模块的使用方法
"""

import os
import sys
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def demo_offline_analysis():
    """演示离线数据分析功能"""
    print("="*60)
    print("📊 演示1：离线数据分析")
    print("="*60)
    
    try:
        from 交通流分析器_修正版 import SUMOTrafficAnalyzer
        
        # 创建分析器
        analyzer = SUMOTrafficAnalyzer('一个交叉口车流.xml', cycle_duration=90)
        
        # 解析数据
        if analyzer.parse_xml_file():
            print("✅ XML文件解析成功")
            
            # 分析第1-3周期
            target_cycles = [1, 2, 3]
            df, processed_data = analyzer.generate_detailed_report(target_cycles)
            
            print(f"✅ 离线分析完成，分析了 {len(target_cycles)} 个周期")
            return df
        else:
            print("❌ XML文件解析失败")
            return None
            
    except Exception as e:
        print(f"❌ 离线分析出错: {e}")
        return None

def demo_quick_test():
    """演示快速测试功能"""
    print("\n" + "="*60)
    print("⚡ 演示2：SUMO快速测试")
    print("="*60)
    
    try:
        # 检查SUMO环境
        if 'SUMO_HOME' not in os.environ:
            print("⚠️ 未设置SUMO_HOME环境变量，跳过SUMO测试")
            return None
        
        from SUMO快速统计测试 import QuickTrafficCounter, test_sumo_environment
        
        # 测试环境
        if not test_sumo_environment():
            print("❌ SUMO环境测试失败")
            return None
        
        # 运行快速测试
        counter = QuickTrafficCounter(cycle_duration=90)
        result = counter.run_quick_test(max_time=180)  # 测试2个周期
        
        if result is not None:
            print("✅ SUMO快速测试成功")
            return result
        else:
            print("❌ SUMO快速测试失败")
            return None
            
    except Exception as e:
        print(f"❌ 快速测试出错: {e}")
        return None

def demo_data_validation():
    """演示数据验证功能"""
    print("\n" + "="*60)
    print("🔍 演示3：数据验证")
    print("="*60)
    
    try:
        from 数据验证 import validate_original_data, validate_analysis_results
        
        # 验证原始数据
        original_stats = validate_original_data()
        
        if original_stats:
            print("✅ 原始数据验证通过")
            
            # 验证分析结果
            result = validate_analysis_results([1, 2])
            
            if result:
                print("✅ 分析结果验证通过")
                return True
            else:
                print("⚠️ 分析结果验证有警告")
                return False
        else:
            print("❌ 原始数据验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据验证出错: {e}")
        return False

def create_comparison_chart(offline_df, sumo_df):
    """创建对比图表"""
    print("\n" + "="*60)
    print("📈 演示4：生成对比图表")
    print("="*60)
    
    try:
        if offline_df is None or sumo_df is None:
            print("⚠️ 缺少数据，跳过图表生成")
            return
        
        # 创建对比图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('SUMO交通流统计系统功能对比', fontsize=16)
        
        # 1. 离线分析结果
        if not offline_df.empty:
            offline_summary = offline_df.groupby('进口方向')['总计'].sum()
            axes[0, 0].bar(offline_summary.index, offline_summary.values, color='skyblue')
            axes[0, 0].set_title('离线分析 - 各方向车辆总数')
            axes[0, 0].set_ylabel('车辆数')
        
        # 2. SUMO实时统计结果
        if not sumo_df.empty:
            sumo_summary = {}
            for direction in ['北', '南', '东', '西']:
                cars_col = f'{direction}_社会车辆'
                buses_col = f'{direction}_公交车'
                if cars_col in sumo_df.columns and buses_col in sumo_df.columns:
                    sumo_summary[direction] = sumo_df[cars_col].sum() + sumo_df[buses_col].sum()
            
            if sumo_summary:
                axes[0, 1].bar(sumo_summary.keys(), sumo_summary.values(), color='lightgreen')
                axes[0, 1].set_title('SUMO实时统计 - 各方向车辆总数')
                axes[0, 1].set_ylabel('车辆数')
        
        # 3. 车辆类型分布（离线）
        if not offline_df.empty:
            vehicle_types = offline_df.groupby('车辆类型')['总计'].sum()
            axes[1, 0].pie(vehicle_types.values, labels=vehicle_types.index, autopct='%1.1f%%')
            axes[1, 0].set_title('离线分析 - 车辆类型分布')
        
        # 4. 周期变化趋势（SUMO）
        if not sumo_df.empty and '周期' in sumo_df.columns:
            total_per_cycle = []
            for _, row in sumo_df.iterrows():
                total = sum([row[f'{d}_社会车辆'] + row[f'{d}_公交车'] 
                           for d in ['北', '南', '东', '西'] 
                           if f'{d}_社会车辆' in row and f'{d}_公交车' in row])
                total_per_cycle.append(total)
            
            axes[1, 1].plot(sumo_df['周期'], total_per_cycle, marker='o', color='orange')
            axes[1, 1].set_title('SUMO实时统计 - 周期变化趋势')
            axes[1, 1].set_xlabel('周期')
            axes[1, 1].set_ylabel('车辆数')
            axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = f'功能演示对比图_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"📁 对比图表已保存: {chart_file}")
        
        plt.show()
        
    except Exception as e:
        print(f"❌ 生成图表时出错: {e}")

def generate_summary_report(offline_df, sumo_df, validation_result):
    """生成总结报告"""
    print("\n" + "="*60)
    print("📋 演示5：生成总结报告")
    print("="*60)
    
    report = []
    report.append("# SUMO交通流统计系统功能演示报告")
    report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 功能测试结果
    report.append("## 功能测试结果")
    report.append("")
    
    # 离线分析
    if offline_df is not None and not offline_df.empty:
        total_vehicles = offline_df['总计'].sum()
        report.append(f"✅ **离线数据分析**: 成功")
        report.append(f"   - 分析车辆总数: {total_vehicles:.1f} 辆")
        report.append(f"   - 分析周期数: {len(offline_df)//8} 个")  # 8行=4方向×2车型
    else:
        report.append("❌ **离线数据分析**: 失败")
    
    # SUMO实时统计
    if sumo_df is not None and not sumo_df.empty:
        sumo_total = 0
        for direction in ['北', '南', '东', '西']:
            cars_col = f'{direction}_社会车辆'
            buses_col = f'{direction}_公交车'
            if cars_col in sumo_df.columns and buses_col in sumo_df.columns:
                sumo_total += sumo_df[cars_col].sum() + sumo_df[buses_col].sum()
        
        report.append(f"✅ **SUMO实时统计**: 成功")
        report.append(f"   - 统计车辆总数: {sumo_total} 辆")
        report.append(f"   - 统计周期数: {len(sumo_df)} 个")
    else:
        report.append("❌ **SUMO实时统计**: 失败或跳过")
    
    # 数据验证
    if validation_result:
        report.append("✅ **数据验证**: 通过")
    else:
        report.append("⚠️ **数据验证**: 有警告或失败")
    
    report.append("")
    report.append("## 系统特点")
    report.append("- 🚀 支持实时SUMO仿真统计")
    report.append("- 📊 提供离线数据分析功能") 
    report.append("- 🔍 包含数据验证机制")
    report.append("- 📈 自动生成可视化图表")
    report.append("- 💾 支持CSV格式数据导出")
    report.append("- ⚙️ 灵活的参数配置")
    
    report.append("")
    report.append("## 技术规格")
    report.append("- 仿真步长: 90秒")
    report.append("- 统计维度: 4个进口方向 × 2种车辆类型")
    report.append("- 支持的仿真时长: 3600秒 (40个周期)")
    report.append("- 数据格式: CSV, XML")
    
    # 保存报告
    report_content = "\n".join(report)
    report_file = f'功能演示报告_{datetime.now().strftime("%Y%m%d_%H%M%S")}.md'
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📁 演示报告已保存: {report_file}")
    print("\n📋 报告内容预览:")
    print("-" * 40)
    print(report_content)

def main():
    """主演示函数"""
    print("🎯 SUMO交通流统计系统 - 完整功能演示")
    print("本演示将展示系统的所有主要功能")
    print("="*80)
    
    # 演示1：离线数据分析
    offline_df = demo_offline_analysis()
    
    # 演示2：SUMO快速测试
    sumo_df = demo_quick_test()
    
    # 演示3：数据验证
    validation_result = demo_data_validation()
    
    # 演示4：生成对比图表
    create_comparison_chart(offline_df, sumo_df)
    
    # 演示5：生成总结报告
    generate_summary_report(offline_df, sumo_df, validation_result)
    
    print("\n" + "="*80)
    print("🎉 完整功能演示结束！")
    print("📁 生成的文件:")
    print("   - 交通流分析结果_*.csv (离线分析结果)")
    print("   - SUMO快速测试结果.csv (SUMO统计结果)")
    print("   - 功能演示对比图_*.png (对比图表)")
    print("   - 功能演示报告_*.md (总结报告)")
    print("\n💡 提示：")
    print("   - 如需完整SUMO仿真，请运行 'SUMO实时交通流统计.py'")
    print("   - 如需详细使用说明，请查看 '使用说明.md'")

if __name__ == "__main__":
    main()
