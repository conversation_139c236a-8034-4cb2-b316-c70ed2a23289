import traci
import pandas as pd
import sumolib
import random
import warnings
warnings.filterwarnings("ignore")
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.font_manager import FontProperties
font = FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf", size=10)
import csv

#相位剩余时间
def phase_remain_time(tls_id):
    global remain_time
    simulation_time=traci.simulation.getTime()
    next_switch_time=traci.trafficlight.getNextSwitch(tls_id)
    phase_id=traci.trafficlight.getPhase(tls_id)
    remain_time=next_switch_time-simulation_time
    return remain_time

#获取信号灯各相位持续时间
def get_duration_time(tls_id):
    tl_def = traci.trafficlight.getAllProgramLogics(tls_id)
    program = traci.trafficlight.getProgram(tls_id)
    obj_logic = [_i for _i in tl_def if _i.getSubID() == program][0]
    phase_duration = []
    for phase in obj_logic.phases:
        a=phase.duration
        phase_duration.append(a)
    return phase_duration

#获取下周期绿灯时间
def next_phase_green_time(tls,a,b):
    if b < a:
        result=sum(tls[b:a])
    else:
        result=sum(tls[b:])+sum(tls[:a])
    return result

#获取速度控制公交前方的社会车排队长度
def car_num_before_bus(bus_id):
    lane_id = traci.vehicle.getLaneID(bus_id)
    car_id_before_bus = traci.lane.getLastStepVehicleIDs(lane_id)
    for i in car_id_before_bus:
        v_type = traci.vehicle.getTypeID(i)
        if v_type =="car":
            car_before_bus.append(i)
    car_before_bus_length = len(car_before_bus)*(car_length + car_gaps)
    return car_before_bus_length


#轨迹推算1
def trajectory_estimation1(lane1_id,bus_id,next_phase_time):

    #获取车辆在路段上的剩余位置
    remain_distance = traci.lane.getLength(lane1_id) - traci.vehicle.getLanePosition(bus_id)
    if remain_time != 0:
        predict_distance = remain_time*traci.vehicle.getSpeed(bus_id)
        predict_distance_max = remain_time * bus_max_speed
        if predict_distance >= remain_distance or predict_distance_max >= remain_distance:
            traci.vehicle.setSpeed(bus_id,bus_max_speed)
        else:
            #计算减速点
            dec_point = (remain_time + signal_cycle) * bus_min_speed
            Dec_point[bus_id] = dec_point

    else:
        predict_distance = next_phase_time * traci.vehicle.getSpeed(bus_id)
        if predict_distance <= remain_distance:
            traci.vehicle.setSpeed(bus_id,bus_min_speed)
        else:
            #计算减速点
            dec_point = next_phase_time*bus_min_speed
            Dec_point[bus_id] = dec_point

    return Dec_point

#轨迹推算1
def trajectory_estimation2(lane1_id,bus_id,next_phase_time):
    #获取车辆在路段上的剩余位置
    remain_distance = traci.lane.getLength(lane1_id) - traci.vehicle.getLanePosition(bus_id)
    if remain_time != 0:
        predict_distance = remain_time*traci.vehicle.getSpeed(bus_id)
        predict_distance_max = remain_time * bus_max_speed
        if predict_distance >= remain_distance or predict_distance_max >= remain_distance:
            pass
        else:
            #计算减速点
            dec_point = (remain_time + next_phase_time) * bus_min_speed
            Dec_point[bus_id] = dec_point

    else:
        predict_distance = next_phase_time * traci.vehicle.getSpeed(bus_id)
        if predict_distance <= remain_distance:
            pass
        else:
            #计算减速点
            dec_point = next_phase_time*bus_min_speed
            Dec_point[bus_id] = dec_point

    return Dec_point

#1-公交速度控制
def speed_control(dec_point,bus_id,lane1_id,remain_time):
    #路段长度
    road_length1=traci.lane.getLength(lane1_id)
    #距离路口的距离
    remain_distance = road_length1- traci.vehicle.getLanePosition(bus_id)
    if dec_point is None:
        traci.vehicle.setSpeed(bus_id,bus_min_speed)
    elif dec_point <= road_length1:
        if road_length1 - traci.vehicle.getLanePosition(bus_id) <= dec_point:
            traci.vehicle.setSpeed(bus_id,bus_min_speed)
    else:
        dec_point2=dec_point-traci.lane.getLength(lane1_id)
        if road_length1-traci.vehicle.getLanePosition(bus_id) > dec_point2:
            traci.vehicle.setSpeed(bus_id,bus_max_speed)

# 获取下一个路段的ID
def get_next_route_id(vehicle_id):
    global next_route_id
    # 获取当前路段的id
    route = traci.vehicle.getRoute(vehicle_id)
    # 获取当前路段的中下标
    index = traci.vehicle.getRouteIndex(vehicle_id)
    # 如果下标小于路段长度减一，说明还有下一个路段
    if index < len(route) - 1:
        # 下一个路段的id
        next_route_id = route[index + 1]
        return next_route_id
    return None


# 1-车路协同控制
signal_plan_after3 = pd.read_csv('优化后的信号配时方案1.csv')
after3_trigger_time_list = signal_plan_after3['Cycle'].unique().tolist()

# 连接sumo
sumo_binary = "E:/Program Files (x86)/Eclipse/Sumo/bin/sumo.exe"
sumo_cfg_file = "NormalIntersection.sumocfg"
traci.start([sumo_binary, "-c", sumo_cfg_file])

# 全局phase(三号交叉口)定义
phase_dict = {'1': 'GrrrGGGrGrrrGGGr',
                '2': 'GrrrGrrGGrrrGrrG',
                '3': 'GGGrGrrrGGGrGrrr',
                '4': 'GrrGGrrrGrrGGrrr'}

junction_id3 = "light3"
after3_phase_duration_dict = signal_plan_after3.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()
after3_logic_dict = {}
for phase_time, duration_dict in after3_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))
    after3_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#车路协同参数定义
car_length = 5
car_gaps = 3
bus_max_speed=14
bus_min_speed=4
signal_cycle=90
person_num_bus=random.randint(25,35)
person_num_car=1.5
vehicle_tracking_after3=[]
track_cycle = []

cycle_counter = 0
current_time = 0
cycle_start_time=current_time

car_count_after3_1 = []
bus_count_after3_1 = []
car_count_after3_2 = []
bus_count_after3_2 = []
car_count_after3_3 = []
bus_count_after3_3 = []
car_count_after3_4 = []
bus_count_after3_4 = []


cycles_after3=[]
car_in_cycle_after3_1=[]
bus_in_cycle_after3_1=[]
person_in_cycle_after3_1=[]
car_in_cycle_after3_2=[]
bus_in_cycle_after3_2=[]
person_in_cycle_after3_2=[]
car_in_cycle_after3_3=[]
bus_in_cycle_after3_3=[]
person_in_cycle_after3_3=[]
car_in_cycle_after3_4=[]
bus_in_cycle_after3_4=[]
person_in_cycle_after3_4=[]


next_phase_time=0
# 被控公交列表
controled_bus=[]
#公交前方的社会车
car_before_bus = []
#公交减速点字典
Dec_point={}

phase_to_lane={"1":["phase3_1_2","phase3_1_1","phase3_3_1","phase3_3_2"],
                "2":["phase3_1_3","phase3_3_3"],
                "3":["phase3_2_1","phase3_2_2","phase3_4_2","phase3_4_1"],
                "4":["phase3_4_3","phase3_2_3"],
               }

vehicle_data = {}
while traci.simulation.getMinExpectedNumber() > 0:
# while traci.simulation.getTime()<1000:
    cycle_counter = current_time//signal_cycle

    # 仿真开始前输入信号配时方案
    if current_time == 0:
        # global phase_time
        for phase_time, logic in after3_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案（每周期切换一次）
    if cycle_counter in after3_trigger_time_list:
        if cycle_counter not in track_cycle:
            traci.trafficlight.setProgram('light3', str(cycle_counter))
            track_cycle.append(cycle_counter)

    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #优先相位剩余绿灯时间计算(0相位)
    phase_duration = get_duration_time(junction_id3)
    # 获取优先相位剩余绿灯时间（0相位）
    if traci.trafficlight.getPhase(junction_id3) == 0:
        remain_time = phase_remain_time(junction_id3)
        print(f"优先相位剩余绿灯时间:{remain_time}")

    else:
        phase = traci.trafficlight.getPhase(junction_id3)
        remain_time2 = phase_remain_time(junction_id3)
        remain_time = 0
        next_phase_time = next_phase_green_time(phase_duration,0,phase) - phase_duration[phase] + remain_time2
        print(f"下一相位切换时间:{next_phase_time}")

    #获取优先相位方向路段上的所有车辆id （0相位）
    vehicle_ids = traci.edge.getLastStepVehicleIDs("phase3_1") + traci.edge.getLastStepVehicleIDs("phase3_3")
    #遍历每个车辆id，将公交车筛选出来（0相位）
    for vehicle_id in vehicle_ids:
        vehicle_type = traci.vehicle.getTypeID(vehicle_id)
        next_route_id = get_next_route_id(vehicle_id)
        if vehicle_type =="bus" and next_route_id == "797981728#0" or next_route_id=="-192585008#1":
            if vehicle_id not in controled_bus:
                orginal_lane = traci.vehicle.getLaneID(vehicle_id)
                Dec_point = trajectory_estimation1(orginal_lane,vehicle_id,next_phase_time)
                controled_bus.append(vehicle_id)

        #南进口道直行
        vehicle_list = traci.lane.getLastStepVehicleIDs('phase3_1_2') + traci.lane.getLastStepVehicleIDs('phase3_1_1')
        for i in vehicle_list:
            #公交车到达时信号状态为绿灯
            if remain_time != 0 and i in controled_bus:
                remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i)- car_num_before_bus(i)

                diatance_max = remain_time * bus_max_speed
                diatance_min = remain_time * bus_min_speed

                if remain_diatance <= 250:
                    if (remain_diatance / bus_max_speed) > remain_time:
                        traci.vehicle.setSpeed(i,bus_min_speed)
                    else:
                        traci.vehicle.setSpeed(i,bus_max_speed)

            #公交车到达时信号状态为红灯
            elif remain_time == 0 and i in controled_bus:
                remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time
                distance_min = bus_min_speed * next_phase_time
                if remain_diatance <= 250:
                    traci.vehicle.setSpeed(i,bus_min_speed)


        #北进口道直行
        vehicle_list = traci.lane.getLastStepVehicleIDs('phase3_3_1') + traci.lane.getLastStepVehicleIDs('phase3_3_2')+ traci.lane.getLastStepVehicleIDs('phase3_3_3')
        for i in vehicle_list:
            if remain_time != 0 and i in controled_bus:
                #任意一条边计算道路长度
                remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)

                diatance_max = remain_time * bus_max_speed
                diatance_min = remain_time * bus_min_speed

                if remain_diatance <= 250:
                    if (remain_diatance / bus_max_speed) > remain_time:
                        traci.vehicle.setSpeed(i,bus_min_speed)
                    else:
                        traci.vehicle.setSpeed(i,bus_max_speed)

            elif remain_time == 0 and i in controled_bus:
                remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time
                distance_min = bus_min_speed * next_phase_time
                if remain_diatance <= 250:
                    traci.vehicle.setSpeed(i,bus_min_speed)


    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)
                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_after3 and vehicle_speed < 0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car" and phase_name == "1":
                            car_count_after3_1.append(vehicle_id)
                            vehicle_tracking_after3.append(vehicle_id)
                        if vehicle_type=="car" and phase_name == "2":
                            car_count_after3_2.append(vehicle_id)
                            vehicle_tracking_after3.append(vehicle_id)
                        if vehicle_type=="car" and phase_name == "3":
                            car_count_after3_3.append(vehicle_id)
                            vehicle_tracking_after3.append(vehicle_id)
                        if vehicle_type=="car" and phase_name == "4":
                            car_count_after3_4.append(vehicle_id)
                            vehicle_tracking_after3.append(vehicle_id)


                        if vehicle_type=="bus" and phase_name == "1":
                            #延误公交车数量
                            bus_count_after3_1.append(vehicle_id)
                            vehicle_tracking_after3.append(vehicle_id)
                        if vehicle_type=="bus" and phase_name == "2":
                            #延误公交车数量
                            bus_count_after3_2.append(vehicle_id)
                            vehicle_tracking_after3.append(vehicle_id)
                        if vehicle_type=="bus" and phase_name == "3":
                            #延误公交车数量
                            bus_count_after3_3.append(vehicle_id)
                            vehicle_tracking_after3.append(vehicle_id)
                        if vehicle_type=="bus" and phase_name == "4":
                            #延误公交车数量
                            bus_count_after3_4.append(vehicle_id)
                            vehicle_tracking_after3.append(vehicle_id)

    if current_time > time_interval_end:
        #统计周期结束时相位1的延误数量
        car_count_cycle_after3_1 = len(car_count_after3_1)
        bus_count_cycle_after3_1 = len(bus_count_after3_1)
        person_count_cycle_after3_1 = int(car_count_cycle_after3_1*person_num_car + bus_count_cycle_after3_1*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位1: 延误社会车={car_count_cycle_after3_1}, 延误公交={bus_count_cycle_after3_1},延误人数={person_count_cycle_after3_1}")

        car_in_cycle_after3_1.append(car_count_cycle_after3_1)
        bus_in_cycle_after3_1.append(bus_count_cycle_after3_1)
        person_in_cycle_after3_1.append(person_count_cycle_after3_1)

        # 统计周期结束时相位2的延误数量
        car_count_cycle_after3_2 = len(car_count_after3_2)
        bus_count_cycle_after3_2 = len(bus_count_after3_2)
        person_count_cycle_after3_2 = int(car_count_cycle_after3_2*person_num_car + bus_count_cycle_after3_2*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位2: 延误社会车={car_count_cycle_after3_2}, 延误公交={bus_count_cycle_after3_2},延误人数={person_count_cycle_after3_2}")

        car_in_cycle_after3_2.append(car_count_cycle_after3_2)
        bus_in_cycle_after3_2.append(bus_count_cycle_after3_2)
        person_in_cycle_after3_2.append(person_count_cycle_after3_2)
        # 统计周期结束时相位3的延误数量
        car_count_cycle_after3_3 = len(car_count_after3_3)
        bus_count_cycle_after3_3 = len(bus_count_after3_3)
        person_count_cycle_after3_3 = int(car_count_cycle_after3_3*person_num_car + bus_count_cycle_after3_3*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位3: 延误社会车={car_count_cycle_after3_3}, 延误公交={bus_count_cycle_after3_3},延误人数={person_count_cycle_after3_3}")

        car_in_cycle_after3_3.append(car_count_cycle_after3_3)
        bus_in_cycle_after3_3.append(bus_count_cycle_after3_3)
        person_in_cycle_after3_3.append(person_count_cycle_after3_3)
        # 统计周期结束时相位4的延误数量
        car_count_cycle_after3_4 = len(car_count_after3_4)
        bus_count_cycle_after3_4 = len(bus_count_after3_4)
        person_count_cycle_after3_4 = int(car_count_cycle_after3_4*person_num_car + bus_count_cycle_after3_4*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位4: 延误社会车={car_count_cycle_after3_4}, 延误公交={bus_count_cycle_after3_4},延误人数={person_count_cycle_after3_4}")
        cycles_after3.append(cycle_counter)
        car_in_cycle_after3_4.append(car_count_cycle_after3_4)
        bus_in_cycle_after3_4.append(bus_count_cycle_after3_4)
        person_in_cycle_after3_4.append(person_count_cycle_after3_4)


        with open('车路协同下单交叉口延误.csv', 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'delay_car_1', 'delay_car_2', 'delay_car_3', 'delay_car_4'
                , 'delay_bus_1', 'delay_bus_2', 'delay_bus_3', 'delay_bus_4'
                , 'delay_person_1', 'delay_person_2', 'delay_person_3', 'delay_person_4']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for i in range(len(cycles_after3)):
                writer.writerow({
                    'cycle': cycles_after3[i],
                    'delay_car_1': car_in_cycle_after3_1[i],
                    'delay_bus_1': bus_in_cycle_after3_1[i],
                    'delay_person_1': person_in_cycle_after3_1[i],
                    'delay_car_2': car_in_cycle_after3_2[i],
                    'delay_bus_2': bus_in_cycle_after3_2[i],
                    'delay_person_2': person_in_cycle_after3_2[i],
                    'delay_car_3': car_in_cycle_after3_3[i],
                    'delay_bus_3': bus_in_cycle_after3_3[i],
                    'delay_person_3': person_in_cycle_after3_3[i],
                    'delay_car_4': car_in_cycle_after3_4[i],
                    'delay_bus_4': bus_in_cycle_after3_4[i],
                    'delay_person_4': person_in_cycle_after3_4[i],

                })

        car_count_after3_1 = []
        bus_count_after3_1 = []
        car_count_after3_2 = []
        bus_count_after3_2 = []
        car_count_after3_3 = []
        bus_count_after3_3 = []
        car_count_after3_4 = []
        bus_count_after3_4 = []

        cycle_start_time = time_interval_end
        cycle_counter += 1

traci.close()

#2-仅信号控制
# 配时方案
signal_plan_after1 = pd.read_csv('优化后的信号配时方案1.csv')
after1_trigger_time_list = signal_plan_after1['Cycle'].unique().tolist()

traci.start([sumo_binary, "-c", sumo_cfg_file])

after1_phase_duration_dict = signal_plan_after1.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

after1_logic_dict = {}

for phase_time, duration_dict in after1_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))
    after1_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#优化信号配时方案开始仿真
vehicle_tracking_after1=[]
current_time = 0
cycle_start_time=current_time
track_cycle = []

car_count_after1_1 = []
car_count_after1_2 = []
car_count_after1_3 = []
car_count_after1_4 = []

bus_count_after1_1 = []
bus_count_after1_2 = []
bus_count_after1_3 = []
bus_count_after1_4 = []


cycles_after1=[]

car_in_cycle_after1_1=[]
car_in_cycle_after1_2=[]
car_in_cycle_after1_3=[]
car_in_cycle_after1_4=[]

bus_in_cycle_after1_1=[]
bus_in_cycle_after1_2=[]
bus_in_cycle_after1_3=[]
bus_in_cycle_after1_4=[]

person_in_cycle_after1_1=[]
person_in_cycle_after1_2=[]
person_in_cycle_after1_3=[]
person_in_cycle_after1_4=[]


while traci.simulation.getMinExpectedNumber() > 0:
# while traci.simulation.getTime()<1000:
    cycle_counter =current_time // signal_cycle
    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in after1_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案
    if cycle_counter in after1_trigger_time_list:
        if cycle_counter not in track_cycle:
            traci.trafficlight.setProgram('light3', str(cycle_counter))
            track_cycle.append(cycle_counter)
    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_after1 and vehicle_speed<0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car" and phase_name == "1":
                            car_count_after1_1.append(vehicle_id)
                            vehicle_tracking_after1.append(vehicle_id)
                        if vehicle_type=="car" and phase_name == "2":
                            car_count_after1_2.append(vehicle_id)
                            vehicle_tracking_after1.append(vehicle_id)
                        if vehicle_type=="car" and phase_name == "3":
                            car_count_after1_3.append(vehicle_id)
                            vehicle_tracking_after1.append(vehicle_id)
                        if vehicle_type=="car" and phase_name == "4":
                            car_count_after1_4.append(vehicle_id)
                            vehicle_tracking_after1.append(vehicle_id)


                        if vehicle_type=="bus" and phase_name == "1":
                            #延误公交车数量
                            bus_count_after1_1.append(vehicle_id)
                            vehicle_tracking_after1.append(vehicle_id)
                        if vehicle_type=="bus" and phase_name == "2":
                            #延误公交车数量
                            bus_count_after1_2.append(vehicle_id)
                            vehicle_tracking_after1.append(vehicle_id)
                        if vehicle_type=="bus" and phase_name == "3":
                            #延误公交车数量
                            bus_count_after1_3.append(vehicle_id)
                            vehicle_tracking_after1.append(vehicle_id)
                        if vehicle_type=="bus" and phase_name == "4":
                            #延误公交车数量
                            bus_count_after1_4.append(vehicle_id)
                            vehicle_tracking_after1.append(vehicle_id)


    if current_time > time_interval_end:
        #统计周期结束时的相位1延误数量
        car_count_cycle_after1_1=len(car_count_after1_1)
        bus_count_cycle_after1_1=len(bus_count_after1_1)
        person_count_cycle_after1_1=int(car_count_cycle_after1_1*person_num_car+bus_count_cycle_after1_1*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位1: 延误社会车={car_count_cycle_after1_1}, 延误公交={bus_count_cycle_after1_1},延误人数={person_count_cycle_after1_1}")

        car_in_cycle_after1_1.append(car_count_cycle_after1_1)
        bus_in_cycle_after1_1.append(bus_count_cycle_after1_1)
        person_in_cycle_after1_1.append(person_count_cycle_after1_1)

        #统计周期结束时的相位2延误数量
        car_count_cycle_after1_2=len(car_count_after1_2)
        bus_count_cycle_after1_2=len(bus_count_after1_2)
        person_count_cycle_after1_2=int(car_count_cycle_after1_2*person_num_car+bus_count_cycle_after1_2*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位1: 延误社会车={car_count_cycle_after1_2}, 延误公交={bus_count_cycle_after1_2},延误人数={person_count_cycle_after1_2}")

        car_in_cycle_after1_2.append(car_count_cycle_after1_2)
        bus_in_cycle_after1_2.append(bus_count_cycle_after1_2)
        person_in_cycle_after1_2.append(person_count_cycle_after1_2)
        #统计周期结束时的相位3延误数量
        car_count_cycle_after1_3=len(car_count_after1_3)
        bus_count_cycle_after1_3=len(bus_count_after1_3)
        person_count_cycle_after1_3=int(car_count_cycle_after1_3*person_num_car+bus_count_cycle_after1_3*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位1: 延误社会车={car_count_cycle_after1_3}, 延误公交={bus_count_cycle_after1_3},延误人数={person_count_cycle_after1_3}")

        car_in_cycle_after1_3.append(car_count_cycle_after1_3)
        bus_in_cycle_after1_3.append(bus_count_cycle_after1_3)
        person_in_cycle_after1_3.append(person_count_cycle_after1_3)
        #统计周期结束时的相位4延误数量
        car_count_cycle_after1_4=len(car_count_after1_4)
        bus_count_cycle_after1_4=len(bus_count_after1_4)
        person_count_cycle_after1_4=int(car_count_cycle_after1_4*person_num_car+bus_count_cycle_after1_4*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位1: 延误社会车={car_count_cycle_after1_4}, 延误公交={bus_count_cycle_after1_4},延误人数={person_count_cycle_after1_4}")
        cycles_after1.append(cycle_counter)
        car_in_cycle_after1_4.append(car_count_cycle_after1_4)
        bus_in_cycle_after1_4.append(bus_count_cycle_after1_4)
        person_in_cycle_after1_4.append(person_count_cycle_after1_4)


        with open('公交优先信号下单交叉口延误.csv', 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'delay_car_1', 'delay_car_2', 'delay_car_3', 'delay_car_4'
                , 'delay_bus_1', 'delay_bus_2', 'delay_bus_3', 'delay_bus_4'
                , 'delay_person_1', 'delay_person_2', 'delay_person_3', 'delay_person_4']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for i in range(len(cycles_after1)):
                writer.writerow({
                    'cycle': cycles_after1[i],
                    'delay_car_1': car_in_cycle_after1_1[i],
                    'delay_car_2': car_in_cycle_after1_2[i],
                    'delay_car_3': car_in_cycle_after1_3[i],
                    'delay_car_4': car_in_cycle_after1_4[i],
                    'delay_bus_1': bus_in_cycle_after1_1[i],
                    'delay_bus_2': bus_in_cycle_after1_2[i],
                    'delay_bus_3': bus_in_cycle_after1_3[i],
                    'delay_bus_4': bus_in_cycle_after1_4[i],
                    'delay_person_1': person_in_cycle_after1_1[i],
                    'delay_person_2': person_in_cycle_after1_2[i],
                    'delay_person_3': person_in_cycle_after1_3[i],
                    'delay_person_4': person_in_cycle_after1_4[i]
                })

        car_count_after1_1 = []
        bus_count_after1_1 = []
        car_count_after1_2 = []
        bus_count_after1_2 = []
        car_count_after1_3 = []
        bus_count_after1_3 = []
        car_count_after1_4 = []
        bus_count_after1_4 = []
        cycle_start_time = time_interval_end

        cycle_counter += 1

traci.close()

#3-初始信号配时方案
# 配时方案
signal_plan_before = pd.read_csv('初始信号配时方案1.csv')
before_trigger_time_list = signal_plan_before['Cycle'].unique().tolist()

traci.start([sumo_binary, "-c", sumo_cfg_file])

before_phase_duration_dict = signal_plan_before.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

before_logic_dict = {}

for phase_time, duration_dict in before_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))

    before_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})


vehicle_tracking_before=[]
current_time = 0
cycle_start_time=current_time
track_cycle = []

car_count_before_1 = []
bus_count_before_1 = []
car_count_before_2 = []
bus_count_before_2 = []
car_count_before_3 = []
bus_count_before_3 = []
car_count_before_4 = []
bus_count_before_4 = []

cycles_before=[]
car_in_cycle_before_1=[]
bus_in_cycle_before_1=[]
person_in_cycle_before_1=[]
car_in_cycle_before_2=[]
bus_in_cycle_before_2=[]
person_in_cycle_before_2=[]
car_in_cycle_before_3=[]
bus_in_cycle_before_3=[]
person_in_cycle_before_3=[]
car_in_cycle_before_4=[]
bus_in_cycle_before_4=[]
person_in_cycle_before_4=[]

simulation_delay_time_before = []
average_delay_time_before = []
bus_delay_time_before = []
while traci.simulation.getMinExpectedNumber() > 0:
# while traci.simulation.getTime()<1000:
    cycle_counter =current_time // signal_cycle
    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in before_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案
    if cycle_counter in before_trigger_time_list:
        if cycle_counter not in track_cycle:
            traci.trafficlight.setProgram('light3', str(cycle_counter))
            track_cycle.append(cycle_counter)
    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_before and vehicle_speed<0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car" and phase_name == "1":
                            car_count_before_1.append(vehicle_id)
                            vehicle_tracking_before.append(vehicle_id)
                        if vehicle_type=="car" and phase_name == "2":
                            car_count_before_2.append(vehicle_id)
                            vehicle_tracking_before.append(vehicle_id)
                        if vehicle_type=="car" and phase_name == "3":
                            car_count_before_3.append(vehicle_id)
                            vehicle_tracking_before.append(vehicle_id)
                        if vehicle_type=="car" and phase_name == "4":
                            car_count_before_4.append(vehicle_id)
                            vehicle_tracking_before.append(vehicle_id)


                        if vehicle_type=="bus" and phase_name == "1":
                            bus_count_before_1.append(vehicle_id)
                            vehicle_tracking_before.append(vehicle_id)
                        if vehicle_type=="bus" and phase_name == "2":
                            bus_count_before_2.append(vehicle_id)
                            vehicle_tracking_before.append(vehicle_id)
                        if vehicle_type=="bus" and phase_name == "3":
                            bus_count_before_3.append(vehicle_id)
                            vehicle_tracking_before.append(vehicle_id)
                        if vehicle_type=="bus" and phase_name == "4":
                            bus_count_before_4.append(vehicle_id)
                            vehicle_tracking_before.append(vehicle_id)


    if current_time > time_interval_end:
        #统计周期结束时的相位1延误
        car_count_cycle_before_1=len(car_count_before_1)
        bus_count_cycle_before_1=len(bus_count_before_1)
        person_count_cycle_before_1=int(car_count_cycle_before_1*person_num_car+bus_count_cycle_before_1*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位1: 延误社会车={car_count_cycle_before_1}, 延误公交={bus_count_cycle_before_1},延误人数={person_count_cycle_before_1}")

        car_in_cycle_before_1.append(car_count_cycle_before_1)
        bus_in_cycle_before_1.append(bus_count_cycle_before_1)
        person_in_cycle_before_1.append(person_count_cycle_before_1)
        #统计周期结束时的相位2延误
        car_count_cycle_before_2=len(car_count_before_2)
        bus_count_cycle_before_2=len(bus_count_before_2)
        person_count_cycle_before_2=int(car_count_cycle_before_2*person_num_car+bus_count_cycle_before_2*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位1: 延误社会车={car_count_cycle_before_2}, 延误公交={bus_count_cycle_before_2},延误人数={person_count_cycle_before_2}")

        car_in_cycle_before_2.append(car_count_cycle_before_2)
        bus_in_cycle_before_2.append(bus_count_cycle_before_2)
        person_in_cycle_before_2.append(person_count_cycle_before_2)
        #统计周期结束时的相位3延误
        car_count_cycle_before_3=len(car_count_before_3)
        bus_count_cycle_before_3=len(bus_count_before_3)
        person_count_cycle_before_3=int(car_count_cycle_before_3*person_num_car+bus_count_cycle_before_3*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位1: 延误社会车={car_count_cycle_before_3}, 延误公交={bus_count_cycle_before_3},延误人数={person_count_cycle_before_3}")

        car_in_cycle_before_3.append(car_count_cycle_before_3)
        bus_in_cycle_before_3.append(bus_count_cycle_before_3)
        person_in_cycle_before_3.append(person_count_cycle_before_3)
        #统计周期结束时的相位4延误
        car_count_cycle_before_4=len(car_count_before_4)
        bus_count_cycle_before_4=len(bus_count_before_4)
        person_count_cycle_before_4=int(car_count_cycle_before_4*person_num_car+bus_count_cycle_before_4*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位1: 延误社会车={car_count_cycle_before_4}, 延误公交={bus_count_cycle_before_4},延误人数={person_count_cycle_before_4}")
        cycles_before.append(cycle_counter)
        car_in_cycle_before_4.append(car_count_cycle_before_4)
        bus_in_cycle_before_4.append(bus_count_cycle_before_4)
        person_in_cycle_before_4.append(person_count_cycle_before_4)


        with open('初始信号方案下单交叉口延误.csv', 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'delay_car_1', 'delay_car_2', 'delay_car_3', 'delay_car_4'
                , 'delay_bus_1', 'delay_bus_2', 'delay_bus_3', 'delay_bus_4'
                , 'delay_person_1', 'delay_person_2', 'delay_person_3', 'delay_person_4']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for i in range(len(cycles_before)):
                writer.writerow({
                    'cycle': cycles_before[i],
                    'delay_car_1': car_in_cycle_before_1[i],
                    'delay_bus_1': bus_in_cycle_before_1[i],
                    'delay_person_1': person_in_cycle_before_1[i],
                    'delay_car_2': car_in_cycle_before_2[i],
                    'delay_bus_2': bus_in_cycle_before_2[i],
                    'delay_person_2': person_in_cycle_before_2[i],
                    'delay_car_3': car_in_cycle_before_3[i],
                    'delay_bus_3': bus_in_cycle_before_3[i],
                    'delay_person_3': person_in_cycle_before_3[i],
                    'delay_car_4': car_in_cycle_before_4[i],
                    'delay_bus_4': bus_in_cycle_before_4[i],
                    'delay_person_4': person_in_cycle_before_4[i]

                })

        car_count_before_1 = []
        bus_count_before_1 = []
        car_count_before_2 = []
        bus_count_before_2 = []
        car_count_before_3 = []
        bus_count_before_3 = []
        car_count_before_4 = []
        bus_count_before_4 = []

        cycle_start_time = time_interval_end
        cycle_counter += 1

traci.close()


#仅速度控制
# 配时方案
signal_plan_after2 = pd.read_csv('初始信号配时方案1.csv')
after2_trigger_time_list = signal_plan_after2['Cycle'].unique().tolist()

traci.start([sumo_binary, "-c", sumo_cfg_file])

after2_phase_duration_dict = signal_plan_after2.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

after2_logic_dict = {}

for phase_time, duration_dict in after2_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))

    after2_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#优化信号配时方案开始仿真
vehicle_tracking_after2=[]
current_time = 0
cycle_start_time=current_time
track_cycle = []

car_count_after2_1 = []
bus_count_after2_1 = []
car_count_after2_2 = []
bus_count_after2_2 = []
car_count_after2_3 = []
bus_count_after2_3 = []
car_count_after2_4 = []
bus_count_after2_4 = []


cycles_after2=[]
car_in_cycle_after2_1=[]
bus_in_cycle_after2_1=[]
person_in_cycle_after2_1=[]
car_in_cycle_after2_2=[]
bus_in_cycle_after2_2=[]
person_in_cycle_after2_2=[]
car_in_cycle_after2_3=[]
bus_in_cycle_after2_3=[]
person_in_cycle_after2_3=[]
car_in_cycle_after2_4=[]
bus_in_cycle_after2_4=[]
person_in_cycle_after2_4=[]


next_phase_time_after_2=0
# 被控公交列表
controled_bus_after_2=[]
#公交前方的社会车
car_before_bus_after_2 = []
#公交减速点字典
Dec_point_after_2={}

simulation_delay_time_after_2 = []
average_delay_time_after_2 = []
bus_delay_time_after_2 = []

while traci.simulation.getMinExpectedNumber() > 0:
# while traci.simulation.getTime()<1000:
    cycle_counter =current_time // signal_cycle
    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in after2_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案
    if cycle_counter in after2_trigger_time_list:
        if cycle_counter not in track_cycle:
            traci.trafficlight.setProgram('light3', str(cycle_counter))
            track_cycle.append(cycle_counter)
    # 开始仿真
    traci.simulationStep()
    current_time += 1

    # 优先相位剩余绿灯时间计算(0相位)
    phase_duration = get_duration_time(junction_id3)
    # print(f"周期{cycle_counter}信号配时方案：{phase_duration}")
    # 获取优先相位剩余绿灯时间（0相位）
    if traci.trafficlight.getPhase(junction_id3) == 0:
        remain_time = phase_remain_time(junction_id3)
        print(f"优先相位剩余绿灯时间:{remain_time}")

    else:
        phase = traci.trafficlight.getPhase(junction_id3)
        remain_time2 = phase_remain_time(junction_id3)
        remain_time = 0
        next_phase_time = next_phase_green_time(phase_duration, 0, phase) - phase_duration[phase] + remain_time2
        print(f"下一相位切换时间:{next_phase_time}")

    # 获取优先相位方向路段上的所有车辆id （0相位）
    vehicle_ids = traci.edge.getLastStepVehicleIDs("phase3_1") + traci.edge.getLastStepVehicleIDs("phase3_3")
    # 遍历每个车辆id，将公交车筛选出来（0相位）
    for vehicle_id in vehicle_ids:
        vehicle_type = traci.vehicle.getTypeID(vehicle_id)
        next_route_id = get_next_route_id(vehicle_id)
        if vehicle_type == "bus" and next_route_id == "797981728#0" or next_route_id=="-192585008#1":
            if vehicle_id not in controled_bus_after_2:
                orginal_lane = traci.vehicle.getLaneID(vehicle_id)
                Dec_point = trajectory_estimation1(orginal_lane, vehicle_id, next_phase_time)
                controled_bus_after_2.append(vehicle_id)

        # 南进口道直行
        vehicle_list = traci.lane.getLastStepVehicleIDs('phase3_1_2') + traci.lane.getLastStepVehicleIDs('phase3_1_1')
        for i in vehicle_list:
            if remain_time != 0 and i in controled_bus:
                remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)

                diatance_max = remain_time * bus_max_speed
                diatance_min = remain_time * bus_min_speed

                if remain_diatance <= 250:
                    if (remain_diatance / bus_max_speed) > remain_time:
                        traci.vehicle.setSpeed(i,bus_min_speed)
                    else:
                        traci.vehicle.setSpeed(i,bus_max_speed)

            elif remain_time == 0 and i in controled_bus:
                # 当前相位为红灯，减速行驶
                remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time
                distance_min = bus_min_speed * next_phase_time

                if remain_diatance <= 250:
                    traci.vehicle.setSpeed(i,bus_min_speed)
        # 北进口道直行
        vehicle_list = traci.lane.getLastStepVehicleIDs('phase3_3_1') + traci.lane.getLastStepVehicleIDs(
            'phase3_3_2') + traci.lane.getLastStepVehicleIDs('phase3_3_3')
        for i in vehicle_list:
            bus_type = traci.vehicle.getTypeID(i)
            if remain_time != 0 and i in controled_bus:
                # 任意一条边计算道路长度
                remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                # print(f"剩余距离：{remain_diatance}米")
                diatance_max = remain_time * bus_max_speed
                # print(f"最大速度行驶距离：{diatance_max}米")
                diatance_min = remain_time * bus_min_speed
                if remain_diatance <= 250:
                    if (remain_diatance / bus_max_speed) > remain_time:
                        traci.vehicle.setSpeed(i,bus_min_speed)
                    else:
                        traci.vehicle.setSpeed(i,bus_max_speed)
            elif remain_time == 0 and i in controled_bus:
                # 当前相位为红灯，减速行驶
                remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time
                distance_min = bus_min_speed * next_phase_time

                if remain_diatance <= 250:
                    traci.vehicle.setSpeed(i,bus_min_speed)

    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_after2 and vehicle_speed<0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car" and phase_name == "1":
                            car_count_after2_1.append(vehicle_id)
                            vehicle_tracking_after2.append(vehicle_id)
                        if vehicle_type=="car" and phase_name == "2":
                            car_count_after2_2.append(vehicle_id)
                            vehicle_tracking_after2.append(vehicle_id)
                        if vehicle_type=="car" and phase_name == "3":
                            car_count_after2_3.append(vehicle_id)
                            vehicle_tracking_after2.append(vehicle_id)
                        if vehicle_type=="car" and phase_name == "4":
                            car_count_after2_4.append(vehicle_id)
                            vehicle_tracking_after2.append(vehicle_id)


                        if vehicle_type=="bus" and phase_name == "1":
                            bus_count_after2_1.append(vehicle_id)
                            vehicle_tracking_after2.append(vehicle_id)
                        if vehicle_type=="bus" and phase_name == "2":
                            bus_count_after2_2.append(vehicle_id)
                            vehicle_tracking_after2.append(vehicle_id)
                        if vehicle_type=="bus" and phase_name == "3":
                            bus_count_after2_3.append(vehicle_id)
                            vehicle_tracking_after2.append(vehicle_id)
                        if vehicle_type=="bus" and phase_name == "4":
                            bus_count_after2_4.append(vehicle_id)
                            vehicle_tracking_after2.append(vehicle_id)


    if current_time > time_interval_end:
        #统计周期结束时的相位1的延误
        car_count_cycle_after2_1=len(car_count_after2_1)
        bus_count_cycle_after2_1=len(bus_count_after2_1)
        person_count_cycle_after2_1=int(car_count_cycle_after2_1*person_num_car+bus_count_cycle_after2_1*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位1: 延误社会车={car_count_cycle_after2_1}, 延误公交={bus_count_cycle_after2_1},延误人数={person_count_cycle_after2_1}")
        car_in_cycle_after2_1.append(car_count_cycle_after2_1)
        bus_in_cycle_after2_1.append(bus_count_cycle_after2_1)
        person_in_cycle_after2_1.append(person_count_cycle_after2_1)
        #统计周期结束时的相位2的延误
        car_count_cycle_after2_2=len(car_count_after2_2)
        bus_count_cycle_after2_2=len(bus_count_after2_2)
        person_count_cycle_after2_2=int(car_count_cycle_after2_2*person_num_car+bus_count_cycle_after2_2*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位2: 延误社会车={car_count_cycle_after2_2}, 延误公交={bus_count_cycle_after2_2},延误人数={person_count_cycle_after2_2}")
        car_in_cycle_after2_2.append(car_count_cycle_after2_2)
        bus_in_cycle_after2_2.append(bus_count_cycle_after2_2)
        person_in_cycle_after2_2.append(person_count_cycle_after2_2)
        #统计周期结束时的相位3的延误
        car_count_cycle_after2_3=len(car_count_after2_3)
        bus_count_cycle_after2_3=len(bus_count_after2_3)
        person_count_cycle_after2_3=int(car_count_cycle_after2_3*person_num_car+bus_count_cycle_after2_3*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位3: 延误社会车={car_count_cycle_after2_3}, 延误公交={bus_count_cycle_after2_3},延误人数={person_count_cycle_after2_3}")
        car_in_cycle_after2_3.append(car_count_cycle_after2_3)
        bus_in_cycle_after2_3.append(bus_count_cycle_after2_3)
        person_in_cycle_after2_3.append(person_count_cycle_after2_3)
        #统计周期结束时的相位4的延误
        car_count_cycle_after2_4=len(car_count_after2_4)
        bus_count_cycle_after2_4=len(bus_count_after2_4)
        person_count_cycle_after2_4=int(car_count_cycle_after2_4*person_num_car+bus_count_cycle_after2_4*person_num_bus)
        print(f"交叉口3-周期{cycle_counter}-相位4: 延误社会车={car_count_cycle_after2_4}, 延误公交={bus_count_cycle_after2_4},延误人数={person_count_cycle_after2_4}")
        cycles_after2.append(cycle_counter)
        car_in_cycle_after2_4.append(car_count_cycle_after2_4)
        bus_in_cycle_after2_4.append(bus_count_cycle_after2_4)
        person_in_cycle_after2_4.append(person_count_cycle_after2_4)


        with open('速度控制下单交叉口延误.csv', 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'delay_car_1', 'delay_car_2', 'delay_car_3', 'delay_car_4'
                , 'delay_bus_1', 'delay_bus_2', 'delay_bus_3', 'delay_bus_4'
                , 'delay_person_1', 'delay_person_2', 'delay_person_3', 'delay_person_4']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for i in range(len(cycles_after2)):
                writer.writerow({
                    'cycle': cycles_after2[i],
                    'delay_car_1': car_in_cycle_after2_1[i],
                    'delay_bus_1': bus_in_cycle_after2_1[i],
                    'delay_person_1': person_in_cycle_after2_1[i],
                    'delay_car_2': car_in_cycle_after2_2[i],
                    'delay_bus_2': bus_in_cycle_after2_2[i],
                    'delay_person_2': person_in_cycle_after2_2[i],
                    'delay_car_3': car_in_cycle_after2_3[i],
                    'delay_bus_3': bus_in_cycle_after2_3[i],
                    'delay_person_3': person_in_cycle_after2_3[i],
                    'delay_car_4': car_in_cycle_after2_4[i],
                    'delay_bus_4': bus_in_cycle_after2_4[i],
                    'delay_person_4': person_in_cycle_after2_4[i]
                })

        car_count_after2_1 = []
        bus_count_after2_1 = []
        car_count_after2_2 = []
        bus_count_after2_2 = []
        car_count_after2_3 = []
        bus_count_after2_3 = []
        car_count_after2_4 = []
        bus_count_after2_4 = []

        cycle_start_time = time_interval_end
        cycle_counter += 1

traci.close()
