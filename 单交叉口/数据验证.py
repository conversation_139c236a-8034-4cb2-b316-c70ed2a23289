#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证脚本
验证分析结果的准确性和数据一致性
"""

import xml.etree.ElementTree as ET
from 交通流分析器_修正版 import SUMOTrafficAnalyzer

def validate_original_data():
    """验证原始XML数据"""
    print("="*60)
    print("📋 原始数据验证")
    print("="*60)
    
    try:
        tree = ET.parse('一个交叉口车流.xml')
        root = tree.getroot()
        
        # 统计原始数据
        total_cars = 0
        total_buses = 0
        direction_stats = {'北': 0, '南': 0, '东': 0, '西': 0}
        
        for flow in root.findall('flow'):
            vehicle_type = flow.get('type', '')
            number = int(flow.get('number', 0))
            flow_id = flow.get('id', '')
            
            # 统计车辆类型
            if vehicle_type == 'car':
                total_cars += number
            elif vehicle_type == 'bus':
                total_buses += number
            
            # 统计方向
            for direction in ['北', '南', '东', '西']:
                if f'{direction}进口道' in flow_id:
                    direction_stats[direction] += number
                    break
        
        print(f"原始数据统计（整个仿真期间）:")
        print(f"  社会车辆总数: {total_cars} 辆")
        print(f"  公交车总数: {total_buses} 辆")
        print(f"  车辆总数: {total_cars + total_buses} 辆")
        
        print(f"\n各方向车辆数:")
        for direction, count in direction_stats.items():
            print(f"  {direction}进口: {count} 辆")
        
        return {
            'total_cars': total_cars,
            'total_buses': total_buses,
            'total_vehicles': total_cars + total_buses,
            'direction_stats': direction_stats
        }
        
    except Exception as e:
        print(f"❌ 验证原始数据时出错: {e}")
        return None

def validate_analysis_results(target_cycles):
    """验证分析结果的准确性"""
    print(f"\n{'='*60}")
    print(f"🔍 分析结果验证 (周期 {target_cycles})")
    print("="*60)
    
    # 获取原始数据统计
    original_stats = validate_original_data()
    if not original_stats:
        return False
    
    # 运行分析
    analyzer = SUMOTrafficAnalyzer('一个交叉口车流.xml', cycle_duration=90)
    if not analyzer.parse_xml_file():
        print("❌ XML解析失败")
        return False
    
    df = analyzer.analyze_cycles(target_cycles)
    
    # 计算分析结果的总数
    analysis_total = df['总计'].sum()
    analysis_cars = df[df['车辆类型'] == '社会车辆']['总计'].sum()
    analysis_buses = df[df['车辆类型'] == '公交车']['总计'].sum()
    
    # 计算期望值（基于周期数）
    num_cycles = len(target_cycles)
    total_cycles = 40  # 总周期数
    
    expected_total = original_stats['total_vehicles'] * num_cycles / total_cycles
    expected_cars = original_stats['total_cars'] * num_cycles / total_cycles
    expected_buses = original_stats['total_buses'] * num_cycles / total_cycles
    
    print(f"数据一致性检查:")
    print(f"  期望总车辆数: {expected_total:.1f} 辆")
    print(f"  分析总车辆数: {analysis_total:.1f} 辆")
    print(f"  误差: {abs(analysis_total - expected_total):.1f} 辆 ({abs(analysis_total - expected_total)/expected_total*100:.1f}%)")
    
    print(f"\n  期望社会车辆: {expected_cars:.1f} 辆")
    print(f"  分析社会车辆: {analysis_cars:.1f} 辆")
    print(f"  误差: {abs(analysis_cars - expected_cars):.1f} 辆 ({abs(analysis_cars - expected_cars)/expected_cars*100:.1f}%)")
    
    print(f"\n  期望公交车: {expected_buses:.1f} 辆")
    print(f"  分析公交车: {analysis_buses:.1f} 辆")
    print(f"  误差: {abs(analysis_buses - expected_buses):.1f} 辆 ({abs(analysis_buses - expected_buses)/expected_buses*100:.1f}%)")
    
    # 验证各方向数据
    print(f"\n各方向数据验证:")
    direction_analysis = df.groupby('进口方向')['总计'].sum()
    
    for direction in ['北', '南', '东', '西']:
        original_dir = original_stats['direction_stats'][direction]
        expected_dir = original_dir * num_cycles / total_cycles
        analysis_dir = direction_analysis.get(direction, 0)
        error_rate = abs(analysis_dir - expected_dir) / expected_dir * 100 if expected_dir > 0 else 0
        
        print(f"  {direction}进口 - 期望: {expected_dir:.1f}, 分析: {analysis_dir:.1f}, 误差: {error_rate:.1f}%")
    
    # 判断验证结果
    total_error_rate = abs(analysis_total - expected_total) / expected_total * 100
    if total_error_rate <= 10:  # 允许10%的误差
        print(f"\n✅ 验证通过！数据一致性良好 (总误差率: {total_error_rate:.1f}%)")
        return True
    else:
        print(f"\n❌ 验证失败！数据误差过大 (总误差率: {total_error_rate:.1f}%)")
        return False

def test_different_scenarios():
    """测试不同场景"""
    print(f"\n{'='*60}")
    print("🧪 多场景测试")
    print("="*60)
    
    test_cases = [
        ([1], "单周期测试"),
        ([3, 4], "双周期测试"),
        ([10, 11, 12, 13, 14], "五周期测试"),
        ([1, 20, 40], "非连续周期测试")
    ]
    
    passed = 0
    total = len(test_cases)
    
    for cycles, description in test_cases:
        print(f"\n🔬 {description} (周期 {cycles})")
        print("-" * 40)
        
        try:
            result = validate_analysis_results(cycles)
            if result:
                print(f"✅ {description} 通过")
                passed += 1
            else:
                print(f"❌ {description} 失败")
        except Exception as e:
            print(f"❌ {description} 出错: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果汇总: {passed}/{total} 通过")
    print("="*60)
    
    if passed == total:
        print("🎉 所有测试都通过了！代码运行正确。")
    else:
        print("⚠️  部分测试失败，建议检查相关功能。")

def main():
    """主函数"""
    print("SUMO交通流分析器 - 数据验证")
    print("本脚本用于验证分析结果的准确性和一致性")
    
    # 首先验证原始数据
    original_stats = validate_original_data()
    
    if original_stats:
        # 验证特定周期的分析结果
        validate_analysis_results([3, 4])
        
        # 测试多种场景
        test_different_scenarios()
    else:
        print("❌ 原始数据验证失败，无法继续测试")

if __name__ == "__main__":
    main()
