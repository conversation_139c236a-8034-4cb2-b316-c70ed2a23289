import traci
import pandas as pd
import sumolib
import random
import warnings
warnings.filterwarnings("ignore")
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.font_manager import FontProperties
font = FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf", size=10)
import csv
#相位剩余时间
def phase_remain_time(tls_id):
    global remain_time
    simulation_time=traci.simulation.getTime()
    next_switch_time=traci.trafficlight.getNextSwitch(tls_id)
    phase_id=traci.trafficlight.getPhase(tls_id)
    remain_time=next_switch_time-simulation_time
    return remain_time

#获取信号灯各相位持续时间
def get_duration_time(tls_id):
    tl_def = traci.trafficlight.getAllProgramLogics(tls_id)
    program = traci.trafficlight.getProgram(tls_id)
    obj_logic = [_i for _i in tl_def if _i.getSubID() == program][0]
    phase_duration = []
    for phase in obj_logic.phases:
        a=phase.duration
        phase_duration.append(a)
    return phase_duration

#获取下周期绿灯时间
def next_phase_green_time(tls,a,b):
    if b < a:
        result=sum(tls[b:a])
    else:
        result=sum(tls[b:])+sum(tls[:a])
    return result

#获取速度控制公交前方的社会车排队长度
def car_num_before_bus(bus_id):
    lane_id = traci.vehicle.getLaneID(bus_id)
    car_id_before_bus = traci.lane.getLastStepVehicleIDs(lane_id)
    for i in car_id_before_bus:
        v_type = traci.vehicle.getTypeID(i)
        if v_type =="car":
            car_before_bus.append(i)
    car_before_bus_length = len(car_before_bus)*(car_length + car_gaps)
    return car_before_bus_length


#轨迹推算1
def trajectory_estimation1(lane1_id,bus_id,next_phase_time):

    #获取车辆在路段上的剩余位置
    remain_distance = traci.lane.getLength(lane1_id) - traci.vehicle.getLanePosition(bus_id)
    if remain_time != 0:
        predict_distance = remain_time*traci.vehicle.getSpeed(bus_id)
        predict_distance_max = remain_time * bus_max_speed
        if predict_distance >= remain_distance or predict_distance_max >= remain_distance:
            traci.vehicle.setSpeed(bus_id,bus_max_speed)
        else:
            #计算减速点
            dec_point = (remain_time + signal_cycle) * bus_min_speed
            Dec_point[bus_id] = dec_point

    else:
        predict_distance = next_phase_time * traci.vehicle.getSpeed(bus_id)
        if predict_distance <= remain_distance:
            traci.vehicle.setSpeed(bus_id,bus_min_speed)
        else:
            #计算减速点
            dec_point = next_phase_time*bus_min_speed
            Dec_point[bus_id] = dec_point

    return Dec_point

#轨迹推算1
def trajectory_estimation2(lane1_id,bus_id,next_phase_time):
    #获取车辆在路段上的剩余位置
    remain_distance = traci.lane.getLength(lane1_id) - traci.vehicle.getLanePosition(bus_id)
    if remain_time != 0:
        predict_distance = remain_time*traci.vehicle.getSpeed(bus_id)
        predict_distance_max = remain_time * bus_max_speed
        if predict_distance >= remain_distance or predict_distance_max >= remain_distance:
            pass
        else:
            #计算减速点
            dec_point = (remain_time + next_phase_time) * bus_min_speed
            Dec_point[bus_id] = dec_point

    else:
        predict_distance = next_phase_time * traci.vehicle.getSpeed(bus_id)
        if predict_distance <= remain_distance:
            pass
        else:
            #计算减速点
            dec_point = next_phase_time*bus_min_speed
            Dec_point[bus_id] = dec_point

    return Dec_point

#1-公交速度控制
def speed_control(dec_point,bus_id,lane1_id,remain_time):
    #路段长度
    road_length1=traci.lane.getLength(lane1_id)
    #距离路口的距离
    remain_distance = road_length1- traci.vehicle.getLanePosition(bus_id)
    if dec_point is None:
        traci.vehicle.setSpeed(bus_id,bus_min_speed)
    elif dec_point <= road_length1:
        if road_length1 - traci.vehicle.getLanePosition(bus_id) <= dec_point:
            traci.vehicle.setSpeed(bus_id,bus_min_speed)
    else:
        dec_point2=dec_point-traci.lane.getLength(lane1_id)
        if road_length1-traci.vehicle.getLanePosition(bus_id) > dec_point2:
            traci.vehicle.setSpeed(bus_id,bus_max_speed)

# 获取下一个路段的ID
def get_next_route_id(vehicle_id):
    global next_route_id
    # 获取当前路段的id
    route = traci.vehicle.getRoute(vehicle_id)
    # 获取当前路段的中下标
    index = traci.vehicle.getRouteIndex(vehicle_id)
    # 如果下标小于路段长度减一，说明还有下一个路段
    if index < len(route) - 1:
        # 下一个路段的id
        next_route_id = route[index + 1]
        return next_route_id
    return None

#绘制轨迹图
def plot_trajs(trajs,lane1,lane2):
    #设置字体
    plt.rcParams['font.serif']= ['Times New Roman']
    plt.rcParams['font.family'] = 'serif'
    fontsize = 12
    plt.rcParams['xtick.labelsize'] = fontsize
    plt.rcParams['ytick.labelsize'] = fontsize
    # 2-画图
    plt.figure( int( 1+np.random.rand()*100), figsize=(6,5))
    for veh_id in trajs.keys():
        txval = trajs[veh_id]
        tt = [ ii[0] for ii in txval ]
        xx = [ ii[1] for ii in txval ]
        ll = [ ii[4] for ii in txval ] # 车道，用于分辨轨迹所在车道
        veh_type = txval[0][5]

        if veh_type == 'bus':
            color = 'red'
        else:
            color = 'gray'

        # 筛选出在指定两条车道上的轨迹
        tt_choose = [tt[ii] for ii in range(len(tt)) if ll[ii] == lane1 or ll[ii] == lane2]
        xx_choose = [xx[ii] for ii in range(len(tt)) if ll[ii] == lane1 or ll[ii] == lane2]

        # 绘制轨迹
        if tt_choose and xx_choose:
            plt.plot(tt_choose, xx_choose, '-', color=color)

    #补一下红绿灯的位置
    x1_light = 989.6
    T1_light = 120
    T1_green = [90, 120]
    T1_red = [0, 90]
    offset1 = 0
    for cc in range(7):
        plt.plot(np.array(T1_green) + cc * T1_light + offset1, [x1_light] * 2, 'g', linewidth=4)
        plt.plot(np.array(T1_red) + cc * T1_light + offset1, [x1_light] * 2, 'r', linewidth=4)
    #其他图绘制
    plt.axis([0, 360, 0, 1100])
    plt.xlabel('Time (s)', fontsize=fontsize + 2, fontweight='bold')
    plt.ylabel('Distance (m)', fontsize=fontsize + 2, fontweight='bold')
    plt.tight_layout()
    plt.savefig("轨迹图", dpi=600)

    plt.show()


# 1-车路协同控制
signal_plan_after3 = pd.read_csv('优化后的信号配时方案1.csv')
after3_trigger_time_list = signal_plan_after3['Cycle'].unique().tolist()

# 连接sumo
sumo_binary = "E:/Program Files (x86)/Eclipse/Sumo/bin/sumo-gui.exe"
sumo_cfg_file = "NormalIntersection.sumocfg"
traci.start([sumo_binary, "-c", sumo_cfg_file])

# 全局phase(三号交叉口)定义
phase_dict = {'1': 'GrrrGGGrGrrrGGGr',
                '2': 'GrrrGrrGGrrrGrrG',
                '3': 'GGGrGrrrGGGrGrrr',
                '4': 'GrrGGrrrGrrGGrrr'}

junction_id3 = "light3"
after3_phase_duration_dict = signal_plan_after3.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()
after3_logic_dict = {}
for phase_time, duration_dict in after3_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))
    after3_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#初始信号配时开始仿真
car_length = 5
car_gaps = 3
bus_max_speed=14
bus_min_speed=4
signal_cycle=90
person_num_bus=random.randint(25,35)
person_num_car=1.5
vehicle_tracking_after3=[]
track_cycle = []

current_time = 0
cycle_start_time=current_time

car_count_after3 = []
bus_count_after3 = []

cycles_after3=[]
car_in_cycle_after3=[]
bus_in_cycle_after3=[]
person_in_cycle_after3=[]

next_phase_time=0
# 被控公交列表
controled_bus=[]
#公交前方的社会车
car_before_bus = []
#公交减速点字典
Dec_point={}
#车辆轨迹
trajs = {}
#计算乘客延误时间
total_person_delay_after3=0
bus_delay_after3 = 0
cycle_delay_after3 = 0
last_cycle_after3 = 0
simulation_delay_time_after3 = []
average_delay_time_after3 = []
bus_delay_time_after3 = []

phase_to_lane={"1":["phase3_1_2","phase3_1_1","phase3_3_1","phase3_3_2"],
                 "2":["phase3_1_3","phase3_3_3"],
                 "3":["phase3_2_1","phase3_2_2","phase3_4_2","phase3_4_1"],
                 "4":["phase3_4_3","phase3_2_3"]}


vehicle_data = {}
# while traci.simulation.getMinExpectedNumber() > 0:
while traci.simulation.getTime()<200:
    cycle_counter = current_time//signal_cycle + 1

    # 仿真开始前输入信号配时方案
    if current_time == 0:
        # global phase_time
        for phase_time, logic in after3_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案（每周期切换一次）
    if cycle_counter in after3_trigger_time_list:
        if cycle_counter not in track_cycle:
            traci.trafficlight.setProgram('light3', str(cycle_counter))
            track_cycle.append(cycle_counter)

    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #优先相位剩余绿灯时间计算(0相位)
    phase_duration = get_duration_time(junction_id3)
    # 获取优先相位剩余绿灯时间（0相位）
    if traci.trafficlight.getPhase(junction_id3) == 0:
        remain_time = phase_remain_time(junction_id3)
        print(f"优先相位剩余绿灯时间:{remain_time}")

    else:
        phase = traci.trafficlight.getPhase(junction_id3)
        remain_time2 = phase_remain_time(junction_id3)
        remain_time = 0
        next_phase_time = next_phase_green_time(phase_duration,0,phase) - phase_duration[phase] + remain_time2
        print(f"下一相位切换时间:{next_phase_time}")

    #获取优先相位方向路段上的所有车辆id （0相位）
    vehicle_ids = traci.edge.getLastStepVehicleIDs("phase3_1") + traci.edge.getLastStepVehicleIDs("phase3_3")
    #遍历每个车辆id，将公交车筛选出来（0相位）
    for vehicle_id in vehicle_ids:
        vehicle_type = traci.vehicle.getTypeID(vehicle_id)
        next_route_id = get_next_route_id(vehicle_id)
        #鉴定车辆类型并区分下一路段的id
        if vehicle_type =="bus" and (next_route_id == "797981728#0" or next_route_id=="-192585008#1"):
            if vehicle_id not in controled_bus:
                controled_bus.append(vehicle_id)

        #南进口道直行
        vehicle_list = traci.lane.getLastStepVehicleIDs('phase3_1_2') + traci.lane.getLastStepVehicleIDs('phase3_1_1')
        for i in vehicle_list:
            #公交车到达时信号状态为绿灯
            if remain_time != 0 and i in controled_bus:
                # veh_mode = traci.vehicle.setLaneChangeMode(i, 0b0000000000)
                remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i)- car_num_before_bus(i)

                # diatance_max = remain_time * bus_max_speed
                # diatance_min = remain_time * bus_min_speed
                #推荐速度
                G_speed = remain_diatance / remain_time

                #剩余绿灯时间以最大速度不能通过
                if remain_diatance / bus_max_speed > remain_time:
                    traci.vehicle.setSpeed(i,bus_min_speed)
                else:
                    traci.vehicle.setSpeed(i,bus_max_speed)

            #公交车到达时信号状态为红灯
            elif remain_time == 0 and i in controled_bus:
                # veh_mode = traci.vehicle.setLaneChangeMode(i, 0b0000000000)
                remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                # distance_max = bus_max_speed * next_phase_time
                # distance_min = bus_min_speed * next_phase_time
                # if remain_diatance <= 100:
                traci.vehicle.setSpeed(i,bus_min_speed)


        #北进口道直行
        vehicle_list = traci.lane.getLastStepVehicleIDs('phase3_3_1') + traci.lane.getLastStepVehicleIDs('phase3_3_2')+ traci.lane.getLastStepVehicleIDs('phase3_3_3')
        for i in vehicle_list:
            if remain_time != 0 and i in controled_bus:
                # veh_mode = traci.vehicle.setLaneChangeMode(i, 0b0000000000)
                #任意一条边计算道路长度
                remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)

                diatance_max = remain_time * bus_max_speed
                diatance_min = remain_time * bus_min_speed

                # if remain_diatance <= 100:
                if (remain_diatance / bus_max_speed) > remain_time:
                    traci.vehicle.setSpeed(i,bus_min_speed)
                else:
                    traci.vehicle.setSpeed(i,bus_max_speed)

            elif remain_time == 0 and i in controled_bus:
                # veh_mode = traci.vehicle.setLaneChangeMode(i, 0b0000000000)
                remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time
                distance_min = bus_min_speed * next_phase_time
                # if remain_diatance <= 100:
                traci.vehicle.setSpeed(i,bus_min_speed)


    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_after3 and vehicle_speed < 0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car":
                            total_person_delay_car = vehicle_waiting_time * person_num_car
                            total_person_delay_after3 += total_person_delay_car
                            cycle_delay_after3 += total_person_delay_car

                            car_count_after3.append(vehicle_id)
                            vehicle_tracking_after3.append(vehicle_id)

                        elif vehicle_type=="bus":
                            #总乘客延误时间
                            total_person_delay_bus = vehicle_waiting_time * person_num_bus
                            total_person_delay_after3 += total_person_delay_bus
                            cycle_delay_after3 += total_person_delay_bus
                            #公交车延误时间
                            bus_delay_after3 += vehicle_waiting_time
                            #延误公交车数量
                            bus_count_after3.append(vehicle_id)
                            vehicle_tracking_after3.append(vehicle_id)
                    elif vehicle_id in vehicle_tracking_after3 and vehicle_speed >0.1:
                        vehicle_tracking_after3.remove(vehicle_id)

        vehicle_current = traci.vehicle.getIDList()#当前车辆id
        for veh_id in list(vehicle_current):
            #新增
            if veh_id not in trajs.keys():
                trajs[veh_id] = []
            #获取信息
            x_in_lane = traci.vehicle.getLanePosition(veh_id)
            v_in_lane = traci.vehicle.getSpeed(veh_id)
            a_in_lane = traci.vehicle.getAcceleration(veh_id)
            lane = traci.vehicle.getLaneID(veh_id)
            veh_type=traci.vehicle.getTypeID(veh_id)
            trajs_list = [current_time, x_in_lane, v_in_lane, a_in_lane, lane,veh_type]  # 新的一条
            trajs[veh_id].append(trajs_list)

    if current_time > time_interval_end:
        #统计周期结束时数据
        car_count_cycle_after3 = len(car_count_after3)
        bus_count_cycle_after3 = len(bus_count_after3)
        person_count_cycle_after3 = int(car_count_cycle_after3*person_num_car + bus_count_cycle_after3*person_num_bus)
        person_delay_time_after3 = int(cycle_delay_after3)
        average_delay_after3 = int(person_delay_time_after3/person_count_cycle_after3)
        bus_delay_cycle_after3 = int(bus_delay_after3)

        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_after3}, Buses={bus_count_cycle_after3}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_after3}")

        cycles_after3.append(cycle_counter)
        car_in_cycle_after3.append(car_count_cycle_after3)
        bus_in_cycle_after3.append(bus_count_cycle_after3)
        person_in_cycle_after3.append(person_count_cycle_after3)
        simulation_delay_time_after3.append(person_delay_time_after3)
        average_delay_time_after3.append(average_delay_after3)
        bus_delay_time_after3.append(bus_delay_cycle_after3)

        with open('车路协同下单交叉口延误.csv', 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'delay_car', 'delay_bus', 'delay_person', 'delay_time',
                          'bus_delay_time']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for i in range(len(cycles_after3)):
                writer.writerow({
                    'cycle': cycles_after3[i],
                    'delay_car': car_in_cycle_after3[i],
                    'delay_bus': bus_in_cycle_after3[i],
                    'delay_person': person_in_cycle_after3[i],
                    'delay_time': simulation_delay_time_after3[i],
                    'bus_delay_time': bus_delay_time_after3[i]
                })

        car_count_after3 = []
        bus_count_after3 = []
        cycle_delay_after3 = 0
        bus_delay_after3 = 0
        cycle_start_time = time_interval_end

traci.close()

plot_trajs(trajs,'phase3_1_2',lane2='phase3_1_1')

