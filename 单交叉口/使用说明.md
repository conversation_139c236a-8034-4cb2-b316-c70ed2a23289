# SUMO交通流实时统计系统使用说明

## 📋 系统概述

本系统提供了完整的SUMO交通仿真实时统计功能，能够在仿真运行过程中实时统计交叉口四个进口方向（东、西、南、北）的社会车辆和公交车数量，并按指定周期（默认90秒）进行汇总。

## 🗂️ 文件说明

### 核心文件
- `SUMO实时交通流统计.py` - 主要的实时统计程序（支持GUI模式）
- `SUMO快速统计测试.py` - 快速测试程序（无GUI模式）
- `交通流分析器_修正版.py` - 离线数据分析工具
- `数据验证.py` - 数据准确性验证工具

### 配置文件
- `NormalIntersection.sumocfg` - SUMO仿真配置文件
- `NormalIntersection.net.xml` - 交通网络文件
- `一个交叉口车流.xml` - 交通流数据文件

### 输出文件
- `SUMO实时交通流统计结果.csv` - 实时统计结果
- `SUMO快速测试结果.csv` - 快速测试结果
- `交通流分析结果_周期*.csv` - 离线分析结果

## 🚀 快速开始

### 环境要求
1. **Python 3.7+**
2. **SUMO 1.8+** 
3. **必需的Python包**：
   ```bash
   pip install pandas numpy traci sumolib
   ```

### 设置SUMO环境变量
```bash
# Windows
set SUMO_HOME=C:\Program Files (x86)\Eclipse\Sumo

# Linux/Mac
export SUMO_HOME=/usr/share/sumo
```

## 📊 使用方法

### 方法1：实时统计（推荐）

```python
# 运行完整的实时统计
python SUMO实时交通流统计.py
```

**功能特点：**
- ✅ 支持GUI模式，可视化仿真过程
- ✅ 实时显示每个周期的统计结果
- ✅ 自动保存详细的CSV结果文件
- ✅ 支持自定义周期时长和最大周期数

### 方法2：快速测试

```python
# 运行快速测试（无GUI）
python SUMO快速统计测试.py
```

**功能特点：**
- ⚡ 快速验证系统功能
- 📊 测试前4个周期的统计
- 💻 命令行模式，适合服务器环境

### 方法3：离线数据分析

```python
# 分析已有的交通流数据
python 交通流分析器_修正版.py
```

**功能特点：**
- 📈 分析XML文件中的历史数据
- 🎯 支持指定周期范围分析
- 📊 生成详细的统计报告和图表

## ⚙️ 配置参数

### 主要参数说明

```python
# 在SUMO实时交通流统计.py中修改以下参数：

cycle_duration = 90      # 周期时长（秒）
max_cycles = 10         # 最大统计周期数
gui_mode = True         # 是否使用GUI模式

# 进口道映射（根据实际网络调整）
approach_edges = {
    '北': 'E41',        # 北进口道边ID
    '南': 'E1.22',      # 南进口道边ID  
    '东': 'E22',        # 东进口道边ID
    '西': 'E37'         # 西进口道边ID
}
```

## 📈 输出结果说明

### 实时统计输出格式

```
📊 周期 1 统计汇总 (时间: 0-90秒)
============================================================
北进口: 社会车辆 35 辆, 公交车 12 辆, 小计 47 辆
南进口: 社会车辆 27 辆, 公交车 7 辆, 小计 34 辆
东进口: 社会车辆 10 辆, 公交车 1 辆, 小计 11 辆
西进口: 社会车辆 8 辆, 公交车 1 辆, 小计 9 辆
周期总计: 101 辆
============================================================
```

### CSV文件格式

| cycle | start_time | end_time | 北_社会车辆 | 北_公交车 | 南_社会车辆 | 南_公交车 | ... |
|-------|------------|----------|-------------|-----------|-------------|-----------|-----|
| 1     | 0          | 90       | 35          | 12        | 27          | 7         | ... |
| 2     | 90         | 180      | 36          | 13        | 28          | 7         | ... |

## 🔧 故障排除

### 常见问题

1. **SUMO_HOME未设置**
   ```
   错误：请设置环境变量 'SUMO_HOME'
   解决：按照上述方法设置SUMO_HOME环境变量
   ```

2. **配置文件不存在**
   ```
   错误：配置文件不存在: NormalIntersection.sumocfg
   解决：确保在正确的目录下运行程序
   ```

3. **TraCI连接失败**
   ```
   错误：启动SUMO仿真失败
   解决：检查SUMO安装是否正确，端口是否被占用
   ```

4. **车辆统计为0**
   ```
   可能原因：进口道边ID映射不正确
   解决：检查approach_edges配置是否与实际网络匹配
   ```

### 调试模式

在代码中添加调试信息：

```python
# 在count_vehicles_in_cycle方法中添加
print(f"调试：当前车辆 {vehicle_id} 在道路 {current_edge}")
```

## 📝 自定义配置

### 修改周期时长

```python
# 修改为120秒周期
counter = SUMORealTimeTrafficCounter("NormalIntersection.sumocfg", cycle_duration=120)
```

### 修改进口道映射

```python
# 根据实际网络修改
self.approach_edges = {
    '北': 'your_north_edge_id',
    '南': 'your_south_edge_id',
    '东': 'your_east_edge_id', 
    '西': 'your_west_edge_id'
}
```

### 添加新的统计维度

```python
# 在统计中添加车道信息
lane_id = traci.vehicle.getLaneID(vehicle_id)
self.cycle_stats[cycle][direction][vehicle_class][lane_id] += 1
```

## 📊 数据验证

运行验证脚本确保数据准确性：

```python
python 数据验证.py
```

验证内容包括：
- ✅ XML解析正确性
- ✅ 方向识别准确性  
- ✅ 数据一致性检查
- ✅ 多场景测试

## 🎯 最佳实践

1. **仿真前准备**
   - 检查所有配置文件完整性
   - 验证网络文件中的边ID
   - 确认交通流数据格式正确

2. **运行时监控**
   - 观察实时统计输出
   - 检查车辆计数是否合理
   - 监控仿真性能

3. **结果分析**
   - 对比不同周期的数据变化
   - 分析各方向流量分布
   - 验证总量与预期是否一致

## 📞 技术支持

如遇到问题，请检查：
1. SUMO版本兼容性
2. Python环境配置
3. 文件路径正确性
4. 网络连接状态

---

**版本信息**
- 系统版本：v1.0
- 更新日期：2025-08-04
- 兼容SUMO版本：1.8+
- Python版本要求：3.7+
