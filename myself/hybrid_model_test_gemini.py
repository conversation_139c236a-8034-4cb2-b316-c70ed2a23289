import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from collections import deque

# --- 1. 根据论文 Table 4 定义3D-MFD参数 (与之前相同) ---
alpha_c = -0.015
beta_c = -0.3
gamma_c = 15.0
alpha_b = -0.06
beta_b = -0.003
gamma_b = 15.0


# --- 2. 核心数学函数 (与之前相同) ---
def get_car_speed(nc, nb):
    speed = alpha_c * nc + beta_c * nb + gamma_c
    return np.maximum(speed, 0)


def get_bus_speed(nc, nb):
    speed = alpha_b * nc + beta_b * nb + gamma_b
    return np.maximum(speed, 0)


def get_network_production(nc, nb):
    vc = get_car_speed(nc, nb)
    vb = get_bus_speed(nc, nb)
    return nc * vc + nb * vb


def get_network_mean_speed(nc, nb):
    total_accumulation = nc + nb
    production = get_network_production(nc, nb)
    mean_speed = np.divide(production, total_accumulation,
                           out=np.zeros_like(production),
                           where=total_accumulation != 0)
    return mean_speed


def get_critical_accumulation(nb):
    if 2 * alpha_c == 0: return np.full_like(nb, float('inf'))
    return -((beta_c + alpha_b) * nb + gamma_c) / (2 * alpha_c)


def get_production_at_capacity(nc, nb):
    n_cr = get_critical_accumulation(nb)
    if np.isscalar(n_cr): n_cr = np.full_like(nc, n_cr)
    return get_network_production(n_cr, nb)


def get_outflow_demand_production(nc, nb):
    n_cr = get_critical_accumulation(nb)
    return np.where(nc <= n_cr, get_network_production(nc, nb), get_production_at_capacity(nc, nb))


def get_entrance_production(nc, nb):
    n_cr = get_critical_accumulation(nb)
    return np.where(nc <= n_cr, get_production_at_capacity(nc, nb), get_production_at_capacity(nc, nb))


# --- 3. 完整复现 Table 3 的混合模型求解器 ---
class HybridModelSolver:
    """
    一个严格遵循论文 Table 3 伪代码的混合模型求解器。
    """

    def __init__(self, initial_nc, initial_nb, car_demand_profile, bus_demand_list, Lc, dt=1):
        # --- 输入 (Input) ---
        self.nc = float(initial_nc)
        self.nb = float(initial_nb)
        self.car_demand_profile = car_demand_profile
        self.bus_demand_list = sorted(bus_demand_list, key=lambda x: x[0])
        self.Lc = float(Lc)
        self.dt = float(dt)
        self.t = 0

        # --- 初始化 (Initialize) ---
        self.bus_exit_list = deque()
        self.bus_entry_list = deque()
        self.bus_queue_list = deque()
        self.bus_circulating_list = []  # 列表存储字典 {'rem_dist': float, 'entry_time': int}

        # 内部状态变量用于FIFO和历史记录
        self.Fc = 0.0  # 累计小汽车入口流量
        self.Fb = 0.0  # 累计公交车入口流量
        self.history = {
            'time': [], 'nc': [], 'nb': [], 'fc': [], 'fb': [], 'Gc': [], 'Gb': [],
            'car_queue': [], 'bus_queue_len': []
        }

    def step(self):
        """
        执行一个仿真步长，严格遵循 Table 3 的逻辑。
        """
        # --- While simulation period: (循环体) ---

        # 1. 计算网络状态
        mean_speed = get_network_mean_speed(self.nc, self.nb)
        n_cr_current = get_critical_accumulation(self.nb)
        is_saturated = self.nc > n_cr_current

        # 2. 计算各模式的入口剩余容量 E_m (Eq. 10)
        total_acc = self.nc + self.nb
        entrance_prod = get_entrance_production(self.nc, self.nb)

        Ec_prod_share = (self.nc / total_acc if total_acc > 0 else 0) * entrance_prod
        Eb_prod_share = (self.nb / total_acc if total_acc > 0 else 0) * entrance_prod

        Ec = (Ec_prod_share / self.Lc) * self.dt if self.Lc > 0 else 0

        # 假设公交车平均行程长度用于计算容量，实际进入取决于 individual trip length
        avg_Lb_in_queue = np.mean([b[1] for b in self.bus_queue_list]) if self.bus_queue_list else 2000.0
        Eb = (Eb_prod_share / avg_Lb_in_queue) * self.dt if avg_Lb_in_queue > 0 else 0

        # 3. 计算小汽车入口流量 fc
        dc = self.car_demand_profile[int(self.t)] * self.dt if self.t < len(self.car_demand_profile) else 0
        fc_unmodified = min(dc, Ec)

        # 4. 处理公交车队列
        # 将到达的公交车从需求列表加入队列
        while self.bus_demand_list and self.bus_demand_list[0][0] <= self.t:
            self.bus_queue_list.append(self.bus_demand_list.pop(0))

        # 5. 计算公交车入口流量 fb (未修正前)
        buses_can_enter = 0
        temp_queue = self.bus_queue_list.copy()
        while buses_can_enter < Eb and temp_queue:
            temp_queue.popleft()
            buses_can_enter += 1
        fb_unmodified = buses_can_enter

        # 6. FIFO 逻辑: 计算到达时间并修正流量
        fc_final, fb_final = fc_unmodified, fb_unmodified

        # 只有在饱和且有需求时才需要FIFO
        if is_saturated and (dc > 0 or len(self.bus_queue_list) > 0):
            # 计算到达时间 t_m* (Eq. 19)
            # 注意：d_m(t) 是速率，需要乘以 dt 得到车辆数
            db = len(self.bus_queue_list)  # 这里的需求是在队列里的公交车数量

            # 使用累计流量计算，更稳健
            tc_star = self.Fc / (dc / self.dt) if dc > 0 else float('inf')
            tb_star = self.Fb / (db / self.dt) if db > 0 else float('inf')

            # 修正延迟较低的模式
            if tc_star < tb_star:  # 小汽车延迟更低，修正小汽车流量 (Eq. 21)
                fc_final = (dc / db) * fb_unmodified if db > 0 else 0
                fc_final = min(fc_unmodified, fc_final)  # 不能超过其自身容量
            elif tb_star < tc_star:  # 公交车延迟更低，修正公交车容量 (Eq. 20)
                modified_Eb = (db / dc) * fc_unmodified if dc > 0 else 0
                modified_Eb = min(Eb, modified_Eb)

                # 根据修正后的容量重新计算能进入的公交车数量
                buses_can_enter_modified = 0
                temp_queue_mod = self.bus_queue_list.copy()
                while buses_can_enter_modified < modified_Eb and temp_queue_mod:
                    temp_queue_mod.popleft()
                    buses_can_enter_modified += 1
                fb_final = buses_can_enter_modified

        # 7. 更新公交车列表
        self.bus_entry_list.clear()
        for _ in range(int(round(fb_final))):
            if self.bus_queue_list:
                entry_time, trip_length = self.bus_queue_list.popleft()
                bus_info = {'rem_dist': trip_length, 'entry_time': self.t}
                self.bus_circulating_list.append(bus_info)
                self.bus_entry_list.append(bus_info)

        # 8. 更新累计入口流量
        self.Fc += fc_final
        self.Fb += fb_final

        # 9. 更新在途公交车状态
        self.bus_exit_list.clear()
        remaining_circulating_buses = []
        for bus in self.bus_circulating_list:
            bus['rem_dist'] -= mean_speed * self.dt
            if bus['rem_dist'] <= 0:
                self.bus_exit_list.append(bus)
            else:
                remaining_circulating_buses.append(bus)
        self.bus_circulating_list = remaining_circulating_buses

        # 10. 计算出口流量 Gc, Gb
        pod = get_outflow_demand_production(self.nc, self.nb)
        Gc = (self.nc / total_acc if total_acc > 0 else 0) * (pod / self.Lc) * self.dt
        Gb = len(self.bus_exit_list)

        # 11. 饱和状态修正 (patch for bus outflow)
        if is_saturated:
            p_cr = get_production_at_capacity(self.nc, self.nb)
            bus_outflow_capacity = (self.nb / total_acc if total_acc > 0 else 0) * (p_cr / avg_Lb_in_queue) * self.dt
            if Gb > bus_outflow_capacity:
                # 实际应用中，会将多余的车辆放回 circulating_list，此处简化为直接限制
                Gb = bus_outflow_capacity

        # 12. 更新累积量
        self.nc += (fc_final - Gc)
        self.nb += (fb_final - Gb)
        self.nc = max(0, self.nc)
        self.nb = max(0, self.nb)

        # 13. 保存历史记录
        self.history['time'].append(self.t)
        self.history['nc'].append(self.nc)
        self.history['nb'].append(self.nb)
        # ... 可以记录更多变量

        self.t += self.dt


# --- 4. 运行仿真 ---
print("正在初始化并准备运行演示性仿真...")
# 使用论文图7中的饱和需求场景参数
sim_duration = 10000
car_demand_rate = 1.35
bus_demand_rate_peak = 0.1
bus_demand_rate_offpeak = 0.015

car_demand_sim = np.zeros(sim_duration)
car_demand_sim[1000:6000] = car_demand_rate

bus_demand_sim_list = []
for t in range(1000):
    if np.random.rand() < bus_demand_rate_offpeak: bus_demand_sim_list.append((t, 2000.0))
for t in range(1000, 6000):
    if np.random.rand() < bus_demand_rate_peak: bus_demand_sim_list.append((t, 2000.0))
for t in range(6000, sim_duration):
    if np.random.rand() < bus_demand_rate_offpeak: bus_demand_sim_list.append((t, 2000.0))

# 运行仿真
solver = HybridModelSolver(0, 0, car_demand_sim, bus_demand_sim_list, 1000.0)
for i in range(sim_duration):
    solver.step()
    if i % 1000 == 0:
        print(f"仿真进行中... 时间: {i}/{sim_duration}")
print("仿真运行完成。")

# --- 5. 绘制仿真结果以验证算法 ---
print("正在绘制仿真结果图...")
fig_sim = plt.figure(figsize=(12, 6))
ax_sim1 = fig_sim.add_subplot(1, 1, 1)
ax_sim1.plot(solver.history['time'], solver.history['nc'], label='小汽车累积量 (nc)')
ax_sim1.plot(solver.history['time'], solver.history['nb'], label='公交车累积量 (nb)')
ax_sim1.set_xlabel('时间 (s)')
ax_sim1.set_ylabel('累积量 (veh)')
ax_sim1.set_title('仿真结果：车辆累积量随时间变化')
ax_sim1.legend()
ax_sim1.grid(True)
plt.show()

# --- 6. 绘制复现的图 6 (a-d) ---
print("正在生成图6的四张子图...")
# 准备绘图数据
nc_range = np.linspace(0, 1000, 200)
nb_range = np.linspace(0, 50, 100)
nc_grid, nb_grid = np.meshgrid(nc_range, nb_range)

production_grid = get_network_production(nc_grid, nb_grid)
mean_speed_grid = get_network_mean_speed(nc_grid, nb_grid)
outflow_demand_grid = get_outflow_demand_production(nc_grid, nb_grid)
entrance_prod_grid = get_entrance_production(nc_grid, nb_grid)

# 绘图
fig = plt.figure(figsize=(16, 14))
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# (a) Network production 3D-NMFD
ax1 = fig.add_subplot(2, 2, 1, projection='3d')
surf1 = ax1.plot_surface(nc_grid, nb_grid, production_grid, cmap='jet', edgecolor='none')
ax1.set_xlabel('$n_c$ (veh)')
ax1.set_ylabel('$n_b$ (veh)')
ax1.set_zlabel('Network production (veh.m/s)')
ax1.set_title('(a) 网络生产能力 3D-NMFD')
ax1.view_init(elev=30, azim=-135)

# (b) Network mean speed 3D-NMFD
ax2 = fig.add_subplot(2, 2, 2, projection='3d')
surf2 = ax2.plot_surface(nc_grid, nb_grid, mean_speed_grid, cmap='jet', edgecolor='none')
ax2.set_xlabel('$n_c$ (veh)')
ax2.set_ylabel('$n_b$ (veh)')
ax2.set_zlabel('Network mean speed (m/s)')
ax2.set_title('(b) 网络平均速度 3D-NMFD')
ax2.view_init(elev=30, azim=-135)

# (c) Outflow demand production
ax3 = fig.add_subplot(2, 2, 3)
contour_levels = np.arange(-500, 4001, 500)
cp3 = ax3.contour(nc_grid, nb_grid, outflow_demand_grid, levels=contour_levels, cmap='jet')
ax3.clabel(cp3, inline=True, fontsize=10, fmt='%d')
ax3.set_xlabel('$n_c$ (veh)')
ax3.set_ylabel('$n_b$ (veh)')
ax3.set_title('(c) 流出需求生产函数')
fig.colorbar(cp3, ax=ax3, label='Outflow demand production (veh.m/s)')

# (d) Entrance production function
ax4 = fig.add_subplot(2, 2, 4)
cp4 = ax4.contour(nc_grid, nb_grid, entrance_prod_grid, levels=contour_levels, cmap='jet')
ax4.clabel(cp4, inline=True, fontsize=10, fmt='%d')
ax4.set_xlabel('$n_c$ (veh)')
ax4.set_ylabel('$n_b$ (veh)')
ax4.set_title('(d) 入口生产函数')
fig.colorbar(cp4, ax=ax4, label='Entrance production function (veh.m/s)')

plt.tight_layout()
plt.show()
print("绘图完成。")
