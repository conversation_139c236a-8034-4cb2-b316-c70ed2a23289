import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import pandas as pd


class HybridBiModalModel:
    def __init__(self):
        # Parameters from Table 4
        # Car speed-NMFD parameters
        self.alpha_c = -0.015
        self.beta_c = -0.3
        self.gamma_c = 15.0

        # Bus speed-NMFD parameters
        self.alpha_b = -0.06
        self.beta_b = -0.003
        self.gamma_b = 15.0

        # Trip lengths
        self.L_c = 1000.0  # Car average trip length (m)
        self.L_b = 2000.0  # Bus average trip length (m)

        # Time step
        self.dt = 1.0  # seconds

    def car_speed_nmfd(self, n_c, n_b):
        """Car speed as function of car and bus accumulations (Eq. 1)"""
        return self.alpha_c * n_c + self.beta_c * n_b + self.gamma_c

    def bus_speed_nmfd(self, n_c, n_b):
        """Bus speed as function of car and bus accumulations (Eq. 2)"""
        return self.alpha_b * n_c + self.beta_b * n_b + self.gamma_b

    def production_3d_nmfd(self, n_c, n_b):
        """Network production 3D-NMFD (Eq. 3)"""
        v_c = self.car_speed_nmfd(n_c, n_b)
        v_b = self.bus_speed_nmfd(n_c, n_b)
        return n_c * v_c + n_b * v_b

    def network_speed_nmfd(self, n_c, n_b):
        """Network average speed (Eq. 4)"""
        if n_c + n_b == 0:
            return 0
        v_c = self.car_speed_nmfd(n_c, n_b)
        v_b = self.bus_speed_nmfd(n_c, n_b)
        return (n_c * v_c + n_b * v_b) / (n_c + n_b)

    def critical_accumulation(self, n_b):
        """Network critical accumulation for different bus shares (Eq. 5)"""
        # 2*alpha_c*n_c + (beta_c + alpha_b)*n_b + gamma_c = 0
        if self.alpha_c == 0:
            return float('inf')
        return -(self.beta_c + self.alpha_b) * n_b / (2 * self.alpha_c) - self.gamma_c / (2 * self.alpha_c)

    def outflow_demand_production(self, n_c, n_b):
        """Outflow demand production function (Eq. 7)"""
        n_cr = self.critical_accumulation(n_b)
        p = self.production_3d_nmfd(n_c, n_b)

        if n_c <= n_cr:
            return p
        else:
            # At capacity level - find critical production
            p_cr = self.production_3d_nmfd(n_cr, n_b)
            return p_cr

    def entrance_production(self, n_c, n_b):
        """Entrance production function (Eq. 9)"""
        n_cr = self.critical_accumulation(n_b)
        p = self.production_3d_nmfd(n_c, n_b)

        if n_c <= n_cr:
            # At capacity level
            p_cr = self.production_3d_nmfd(n_cr, n_b)
            return p_cr
        else:
            return p

    def car_outflow(self, n_c, n_b):
        """Car outflow calculation (Eq. 18)"""
        n_cr = self.critical_accumulation(n_b)
        n_total = n_c + n_b

        if n_total == 0:
            return 0

        if n_c <= n_cr:
            # Free flow
            p = self.production_3d_nmfd(n_c, n_b)
            return (n_c / n_total) * p / self.L_c
        else:
            # Saturated
            p_cr = self.production_3d_nmfd(n_cr, n_b)
            return (n_c / n_total) * p_cr / self.L_c

    def remained_spaces_cars(self, n_c, n_b):
        """Remained spaces for cars (Eq. 10)"""
        n_cr = self.critical_accumulation(n_b)
        n_total = n_c + n_b

        if n_total == 0:
            return 0

        if n_c <= n_cr:
            # Free flow
            p_cr = self.production_3d_nmfd(n_cr, n_b)
            return (n_c / n_total) * p_cr / self.L_c
        else:
            # Saturated
            p = self.production_3d_nmfd(n_c, n_b)
            return (n_c / n_total) * p / self.L_c

    def remained_spaces_buses(self, n_c, n_b):
        """Remained spaces for buses (Eq. 10)"""
        n_cr = self.critical_accumulation(n_b)
        n_total = n_c + n_b

        if n_total == 0:
            return 0

        if n_c <= n_cr:
            # Free flow
            p_cr = self.production_3d_nmfd(n_cr, n_b)
            return (n_b / n_total) * p_cr / self.L_b
        else:
            # Saturated
            p = self.production_3d_nmfd(n_c, n_b)
            return (n_b / n_total) * p / self.L_b


def create_figure_6():
    """Reproduce Figure 6 from the paper"""
    model = HybridBiModalModel()

    # Create meshgrid for 3D plots
    n_c_range = np.linspace(0, 800, 50)
    n_b_range = np.linspace(0, 100, 25)
    N_C, N_B = np.meshgrid(n_c_range, n_b_range)

    # Calculate surfaces
    PRODUCTION = np.zeros_like(N_C)
    NETWORK_SPEED = np.zeros_like(N_C)
    OUTFLOW_DEMAND = np.zeros_like(N_C)
    ENTRANCE_PROD = np.zeros_like(N_C)

    for i in range(N_C.shape[0]):
        for j in range(N_C.shape[1]):
            n_c = N_C[i, j]
            n_b = N_B[i, j]

            PRODUCTION[i, j] = model.production_3d_nmfd(n_c, n_b)
            NETWORK_SPEED[i, j] = model.network_speed_nmfd(n_c, n_b)
            OUTFLOW_DEMAND[i, j] = model.outflow_demand_production(n_c, n_b)
            ENTRANCE_PROD[i, j] = model.entrance_production(n_c, n_b)

    # Create the figure
    fig = plt.figure(figsize=(16, 12))

    # (a) Production 3D-NMFD
    ax1 = fig.add_subplot(2, 2, 1, projection='3d')
    surf1 = ax1.plot_surface(N_C, N_B, PRODUCTION, cmap='viridis', alpha=0.8)
    ax1.set_xlabel('Car Accumulation $n_c$ (veh)')
    ax1.set_ylabel('Bus Accumulation $n_b$ (veh)')
    ax1.set_zlabel('Production $p$ (veh·m/s)')
    ax1.set_title('(a) Network Production 3D-NMFD')
    ax1.view_init(elev=20, azim=45)

    # (b) Speed-NMFD
    ax2 = fig.add_subplot(2, 2, 2, projection='3d')
    surf2 = ax2.plot_surface(N_C, N_B, NETWORK_SPEED, cmap='plasma', alpha=0.8)
    ax2.set_xlabel('Car Accumulation $n_c$ (veh)')
    ax2.set_ylabel('Bus Accumulation $n_b$ (veh)')
    ax2.set_zlabel('Network Speed $v$ (m/s)')
    ax2.set_title('(b) Network Speed-NMFD')
    ax2.view_init(elev=20, azim=45)

    # (c) Outflow demand production
    ax3 = fig.add_subplot(2, 2, 3, projection='3d')
    surf3 = ax3.plot_surface(N_C, N_B, OUTFLOW_DEMAND, cmap='coolwarm', alpha=0.8)
    ax3.set_xlabel('Car Accumulation $n_c$ (veh)')
    ax3.set_ylabel('Bus Accumulation $n_b$ (veh)')
    ax3.set_zlabel('Outflow Demand Production $p_{od}$ (veh·m/s)')
    ax3.set_title('(c) Outflow Demand Production')
    ax3.view_init(elev=20, azim=45)

    # (d) Entrance production function
    ax4 = fig.add_subplot(2, 2, 4, projection='3d')
    surf4 = ax4.plot_surface(N_C, N_B, ENTRANCE_PROD, cmap='inferno', alpha=0.8)
    ax4.set_xlabel('Car Accumulation $n_c$ (veh)')
    ax4.set_ylabel('Bus Accumulation $n_b$ (veh)')
    ax4.set_zlabel('Entrance Production $E_p$ (veh·m/s)')
    ax4.set_title('(d) Entrance Production Function')
    ax4.view_init(elev=20, azim=45)

    plt.tight_layout()
    plt.show()

    return fig


class Bus:
    """Bus object for trip-based tracking"""

    def __init__(self, entry_time, trip_length):
        self.entry_time = entry_time
        self.trip_length = trip_length
        self.remaining_distance = trip_length
        self.exit_time = None


class HybridSolver:
    """Hybrid model solver implementing Table 3 algorithm"""

    def __init__(self, model):
        self.model = model
        self.reset_state()

    def reset_state(self):
        """Reset solver state"""
        self.exit_bus_list = []
        self.entry_bus_list = []
        self.queue_bus_list = []
        self.circulating_bus_list = []

        # Cumulative inflows for FIFO calculation
        self.F_c = 0.0  # Cumulative car inflow
        self.F_b = 0.0  # Cumulative bus inflow

    def solve_hybrid_model(self, simulation_time, car_demand, bus_demand_list,
                           initial_n_c=0, initial_n_b=0):
        """
        Solve hybrid model following Table 3 algorithm

        Args:
            simulation_time: Total simulation time (s)
            car_demand: Array of car demand profile (veh/s)
            bus_demand_list: List of Bus objects with entry times
            initial_n_c: Initial car accumulation
            initial_n_b: Initial bus accumulation
        """

        # Initialize state variables
        n_c = initial_n_c
        n_b = initial_n_b

        # Results storage
        results = {
            'time': [],
            'n_c': [],
            'n_b': [],
            'f_c': [],  # car inflow
            'f_b': [],  # bus inflow
            'G_c': [],  # car outflow
            'G_b': [],  # bus outflow
            'mean_speed': [],
            'outflow_capacity': []
        }

        # Initialize bus demand list sorted by entry time
        bus_demand_sorted = sorted(bus_demand_list, key=lambda x: x.entry_time)
        bus_demand_index = 0

        # Main simulation loop
        for t in range(simulation_time):
            # Store current time
            results['time'].append(t)

            # Calculate mean speed of reservoir
            v_mean = self.model.network_speed_nmfd(n_c, n_b)
            results['mean_speed'].append(v_mean)

            # Determine outflow capacity
            p_cr = self.model.production_3d_nmfd(
                self.model.critical_accumulation(n_b), n_b
            )
            results['outflow_capacity'].append(p_cr)

            # Calculate remained spaces for each mode
            E_c = self.model.remained_spaces_cars(n_c, n_b)
            E_b = self.model.remained_spaces_buses(n_c, n_b)

            # Determine inflow of cars
            d_c = car_demand[min(t, len(car_demand) - 1)]  # Current car demand
            f_c = min(d_c, E_c)  # Car inflow

            # Add buses to queue if their entry time matches current time
            while (bus_demand_index < len(bus_demand_sorted) and
                   bus_demand_sorted[bus_demand_index].entry_time == t):
                self.queue_bus_list.append(bus_demand_sorted[bus_demand_index])
                bus_demand_index += 1

            # Sort queue bus list based on arrival time
            self.queue_bus_list.sort(key=lambda x: x.entry_time)

            # Process buses in queue
            f_b = 0
            buses_to_remove = []
            for i, bus in enumerate(self.queue_bus_list):
                if E_b > 0:  # Space available
                    # Move bus from queue to entry list
                    self.entry_bus_list.append(bus)
                    # Move bus to circulating list
                    self.circulating_bus_list.append(bus)
                    buses_to_remove.append(i)
                    E_b -= 1  # Reduce available space
                    f_b += 1  # Increment bus inflow
                else:
                    # Not enough space, bus stays in queue
                    bus.entry_time += 1  # Add delay

            # Remove processed buses from queue
            for i in reversed(buses_to_remove):
                self.queue_bus_list.pop(i)

            # Calculate cumulative inflows for FIFO
            self.F_c += f_c
            self.F_b += f_b

            # Arrival time calculation for FIFO
            if d_c > 0:
                t_star_c = self.F_c / d_c
            else:
                t_star_c = float('inf')

            # For buses, use entry time directly
            if f_b > 0 and len(self.entry_bus_list) > 0:
                t_star_b = np.mean([bus.entry_time for bus in self.entry_bus_list[-int(f_b):]])
            else:
                t_star_b = float('inf')

            # FIFO modification - modify inflow of mode with lower delay
            if t_star_c < t_star_b and d_c > 0:
                # Cars have lower delay, modify bus inflow
                # This is simplified - in practice would need bus demand
                pass
            elif t_star_b < t_star_c and f_b > 0:
                # Buses have lower delay, modify car inflow
                pass

            # Sort circulating bus list based on remaining trip distance
            self.circulating_bus_list.sort(key=lambda x: x.remaining_distance)

            # Update circulating buses
            G_b = 0  # Bus outflow
            buses_to_exit = []

            for i, bus in enumerate(self.circulating_bus_list):
                # Update remaining trip distance
                bus.remaining_distance -= v_mean * self.model.dt

                # Check if bus completes trip
                if bus.remaining_distance <= 0:
                    bus.exit_time = t
                    self.exit_bus_list.append(bus)
                    buses_to_exit.append(i)
                    G_b += 1

            # Remove exited buses from circulating list
            for i in reversed(buses_to_exit):
                self.circulating_bus_list.pop(i)

            # Calculate car outflow
            G_c = self.model.car_outflow(n_c, n_b)

            # Apply saturation constraints if needed
            n_cr = self.model.critical_accumulation(n_b)
            if n_c > n_cr:  # Saturated conditions
                # Apply capacity constraints to outflows
                max_outflow_capacity = p_cr / max(self.model.L_c, self.model.L_b)
                total_current_outflow = G_c + G_b
                if total_current_outflow > max_outflow_capacity:
                    # Scale outflows proportionally
                    scale_factor = max_outflow_capacity / total_current_outflow
                    G_c *= scale_factor
                    G_b = int(G_b * scale_factor)

                    # Adjust bus exit list accordingly
                    excess_buses = len(self.exit_bus_list) - int(G_b)
                    if excess_buses > 0:
                        # Move excess buses back to circulating
                        for _ in range(excess_buses):
                            if self.exit_bus_list:
                                bus = self.exit_bus_list.pop()
                                bus.remaining_distance = 1  # Small remaining distance
                                self.circulating_bus_list.append(bus)

            # Update car accumulation
            n_c += f_c * self.model.dt - G_c * self.model.dt
            n_c = max(0, n_c)  # Ensure non-negative

            # Update bus accumulation from lists
            n_b = len(self.circulating_bus_list)

            # Store results
            results['n_c'].append(n_c)
            results['n_b'].append(n_b)
            results['f_c'].append(f_c)
            results['f_b'].append(f_b)
            results['G_c'].append(G_c)
            results['G_b'].append(G_b)

            # Clear entry and exit lists for next iteration
            self.entry_bus_list = []
            self.exit_bus_list = []

        return results


# Test the implementation
if __name__ == "__main__":
    # Create and display Figure 6
    print("Creating Figure 6 reproduction...")
    fig = create_figure_6()

    # Test hybrid solver with simple scenario
    print("\nTesting hybrid solver...")

    model = HybridBiModalModel()
    solver = HybridSolver(model)

    # Simple test scenario
    simulation_time = 100
    car_demand = np.ones(simulation_time) * 0.5  # 0.5 veh/s constant demand

    # Create some buses
    bus_demand_list = []
    for i in range(0, 50, 10):  # Bus every 10 seconds
        bus_demand_list.append(Bus(entry_time=i, trip_length=2000))

    # Solve
    results = solver.solve_hybrid_model(
        simulation_time=simulation_time,
        car_demand=car_demand,
        bus_demand_list=bus_demand_list,
        initial_n_c=10,
        initial_n_b=2
    )

    # Plot simple test results
    fig2, axes = plt.subplots(2, 2, figsize=(12, 8))

    axes[0, 0].plot(results['time'], results['n_c'], label='Cars')
    axes[0, 0].plot(results['time'], results['n_b'], label='Buses')
    axes[0, 0].set_ylabel('Accumulation (veh)')
    axes[0, 0].set_title('Accumulations')
    axes[0, 0].legend()
    axes[0, 0].grid(True)

    axes[0, 1].plot(results['time'], results['f_c'], label='Cars')
    axes[0, 1].plot(results['time'], results['f_b'], label='Buses')
    axes[0, 1].set_ylabel('Inflow (veh/s)')
    axes[0, 1].set_title('Inflows')
    axes[0, 1].legend()
    axes[0, 1].grid(True)

    axes[1, 0].plot(results['time'], results['G_c'], label='Cars')
    axes[1, 0].plot(results['time'], results['G_b'], label='Buses')
    axes[1, 0].set_ylabel('Outflow (veh/s)')
    axes[1, 0].set_title('Outflows')
    axes[1, 0].legend()
    axes[1, 0].grid(True)

    axes[1, 1].plot(results['time'], results['mean_speed'])
    axes[1, 1].set_ylabel('Mean Speed (m/s)')
    axes[1, 1].set_xlabel('Time (s)')
    axes[1, 1].set_title('Network Mean Speed')
    axes[1, 1].grid(True)

    plt.tight_layout()
    plt.show()

    print("Implementation complete!")
    print(f"Final car accumulation: {results['n_c'][-1]:.2f}")
    print(f"Final bus accumulation: {results['n_b'][-1]:.2f}")