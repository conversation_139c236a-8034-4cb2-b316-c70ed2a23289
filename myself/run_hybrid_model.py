import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D  # 引入3D绘图库
from collections import deque
from tqdm import tqdm


# --- 1. NMFD类: 封装网络宏观特性 ---
# 这个类使用您已经成功拟合出的参数。
class NMFD:
    """
    封装3D网络宏观基本图 (3D-NMFD) 的相关计算。
    """

    def __init__(self, params):
        # !!! 使用您成功拟合出的参数 !!!
        self.gamma_c = params['gamma_c']
        self.alpha_c = params['alpha_c']
        self.beta_c = params['beta_c']
        self.gamma_b = params['gamma_b']
        self.alpha_b = params['alpha_b']
        self.beta_b = params['beta_b']

    def v_c(self, n_c, n_b):
        # 公式(1): 计算小汽车平均速度
        return np.maximum(0, self.gamma_c + self.alpha_c * n_c + self.beta_c * n_b)

    def v_b(self, n_c, n_b):
        # 公式(2): 计算公交车平均速度
        return np.maximum(0, self.gamma_b + self.alpha_b * n_c + self.beta_b * n_b)

    def p(self, n_c, n_b):
        # 公式(3): 计算网络总产出
        return n_c * self.v_c(n_c, n_b) + n_b * self.v_b(n_c, n_b)

    def v(self, n_c, n_b):
        # 公式(4): 计算网络平均速度
        total_n = n_c + n_b
        # 避免除以零
        if isinstance(total_n, np.ndarray):
            # 处理numpy数组的情况
            avg_v = np.zeros_like(total_n, dtype=float)
            non_zero_mask = total_n > 0
            avg_v[non_zero_mask] = self.p(n_c[non_zero_mask], n_b[non_zero_mask]) / total_n[non_zero_mask]
            avg_v[~non_zero_mask] = self.gamma_c  # or some other default
            return avg_v
        else:
            # 处理单个值的情况
            if total_n == 0:
                return self.gamma_c
            return self.p(n_c, n_b) / total_n

    def n_cr(self, n_b):
        # 公式(5): 计算网络临界小汽车累积量
        denominator = 2 * self.alpha_c
        if abs(denominator) < 1e-9: return float('inf')
        return -(self.gamma_c + (self.beta_c + self.alpha_b) * n_b) / denominator

    def p_cr(self, n_c, n_b):
        # 计算在临界状态下的网络产出 (容量)
        n_crit = self.n_cr(n_b)
        return self.p(n_crit, n_b)

    def p_od(self, n_c, n_b):
        # 公式(7): 计算出口需求产出函数
        n_crit = self.n_cr(n_b)
        if n_c <= n_crit:
            return self.p(n_c, n_b)
        else:
            return self.p_cr(n_c, n_b)

    def E_p(self, n_c, n_b):
        # 公式(9): 计算入口产出函数
        n_crit = self.n_cr(n_b)
        if n_c <= n_crit:
            return self.p_cr(n_c, n_b)
        else:
            return self.p(n_c, n_b)


# --- 2. 宏观混合模型模拟器 ---
class HybridSimulator:
    """
    实现论文表3中的宏观混合模型算法。
    """

    def __init__(self, nmfd, L_c, L_b, sim_time, dt, demand_c, demand_b):
        self.nmfd = nmfd
        self.L_c = L_c
        self.L_b = L_b
        self.sim_time = sim_time
        self.dt = dt
        self.time_steps = int(sim_time / dt)
        self.demand_c = demand_c
        self.demand_b = demand_b

        # 初始化结果存储
        self.n_c_hist = np.zeros(self.time_steps)
        self.n_b_hist = np.zeros(self.time_steps)
        self.f_c_hist = np.zeros(self.time_steps)
        self.f_b_hist = np.zeros(self.time_steps)
        self.G_c_hist = np.zeros(self.time_steps)
        self.G_b_hist = np.zeros(self.time_steps)

    def run(self):
        n_c = 0
        F_c, F_b = 0, 0  # 累计入口流量

        bus_circulating_list = deque()
        bus_queue = deque()
        next_bus_id = 0

        print("正在运行宏观混合模型仿真...")
        for t_idx in tqdm(range(self.time_steps), desc="仿真进度"):
            t = t_idx * self.dt

            # --- 1. 新到达的公交车加入队列 ---
            num_new_buses = self.demand_b[t_idx] * self.dt
            # 处理非整数的车辆到达
            if np.random.rand() < num_new_buses - int(num_new_buses):
                num_new_buses = int(num_new_buses) + 1
            else:
                num_new_buses = int(num_new_buses)

            for _ in range(num_new_buses):
                bus_queue.append({'id': next_bus_id, 'entry_time': t, 'rem_dist': self.L_b})
                next_bus_id += 1

            # --- 2. 计算当前状态 ---
            n_b = len(bus_circulating_list)
            n_tot = n_c + n_b

            # --- 3. 计算出口流量 ---
            # 小汽车出口 (公式18)
            outflow_prod = self.nmfd.p_od(n_c, n_b)
            G_c = (n_c / n_tot * outflow_prod / self.L_c) * self.dt if n_tot > 0 else 0

            # 公交车出口 (基于行程)
            G_b_count = 0
            exiting_buses = []
            current_speed = self.nmfd.v(n_c, n_b)
            dist_traveled = current_speed * self.dt

            for bus in bus_circulating_list:
                bus['rem_dist'] -= dist_traveled
                if bus['rem_dist'] <= 0:
                    exiting_buses.append(bus)

            if exiting_buses:
                exiting_ids = {b['id'] for b in exiting_buses}
                bus_circulating_list = deque([b for b in bus_circulating_list if b['id'] not in exiting_ids])
                G_b_count = len(exiting_buses)

            # --- 4. 计算入口流量 ---
            inflow_prod = self.nmfd.E_p(n_c, n_b)

            E_c = (n_c / n_tot * inflow_prod / self.L_c) * self.dt if n_tot > 0 else inflow_prod / self.L_c * self.dt
            E_b_space = (
                                    n_b / n_tot * inflow_prod / self.L_b) * self.dt if n_tot > 0 else inflow_prod / self.L_b * self.dt

            f_c = min(self.demand_c[t_idx] * self.dt, E_c)
            f_b = min(E_b_space, len(bus_queue))

            # --- 5. 混合模型的FIFO算法 ---
            d_c_curr = self.demand_c[t_idx]
            d_b_curr = self.demand_b[t_idx]

            f_c_final, f_b_final = f_c, f_b

            if d_c_curr > 1e-6 and d_b_curr > 1e-6:
                t_c_star = (F_c + f_c) / d_c_curr if d_c_curr > 0 else 0
                t_b_star = (F_b + f_b) / d_b_curr if d_b_curr > 0 else 0

                if t_b_star < t_c_star:
                    f_c_final = (d_c_curr / d_b_curr) * f_b
                elif t_c_star < t_b_star:
                    E_b_modified = (d_b_curr / d_c_curr) * f_c
                    f_b_final = min(E_b_modified, len(bus_queue))

            # 实际移动公交车
            num_buses_to_enter = int(round(f_b_final))
            for _ in range(num_buses_to_enter):
                if bus_queue:
                    bus = bus_queue.popleft()
                    bus_circulating_list.append(bus)

            # --- 6. 更新状态 (一阶欧拉法) ---
            n_c += f_c_final - G_c
            n_c = max(0, n_c)

            F_c += f_c_final
            F_b += num_buses_to_enter

            # --- 7. 存储结果 ---
            self.n_c_hist[t_idx] = n_c
            self.n_b_hist[t_idx] = len(bus_circulating_list)
            self.f_c_hist[t_idx] = f_c_final / self.dt
            self.f_b_hist[t_idx] = num_buses_to_enter / self.dt
            self.G_c_hist[t_idx] = G_c / self.dt
            self.G_b_hist[t_idx] = G_b_count / self.dt

        print("仿真完成。")
        return self.get_results()

    def get_results(self):
        return {
            "time": np.arange(0, self.sim_time, self.dt),
            "n_c": self.n_c_hist, "n_b": self.n_b_hist,
            "f_c": self.f_c_hist, "f_b": self.f_b_hist,
            "G_c": self.G_c_hist, "G_b": self.G_b_hist,
        }


# --- 3. 新增! 3D-MFD 可视化函数 ---
def plot_3d_mfd(nmfd_instance, results):
    """
    绘制3D-MFD理论曲面，并将仿真轨迹叠加在上面。
    """
    print("\n正在生成3D-MFD可视化图...")
    fig = plt.figure(figsize=(12, 9))
    ax = fig.add_subplot(111, projection='3d')

    # --- 绘制理论曲面 ---
    # 创建网格数据
    n_c_max = max(1, np.max(results['n_c'])) * 1.2
    n_b_max = max(1, np.max(results['n_b'])) * 1.2
    n_c_grid = np.linspace(0, n_c_max, 50)
    n_b_grid = np.linspace(0, n_b_max, 50)
    N_c, N_b = np.meshgrid(n_c_grid, n_b_grid)

    # 计算每个点的网络产出
    P = nmfd_instance.p(N_c, N_b)

    # 绘制曲面
    ax.plot_surface(N_c, N_b, P, cmap='viridis', edgecolor='none', alpha=0.7)

    # --- 绘制仿真轨迹 ---
    sim_n_c = results['n_c']
    sim_n_b = results['n_b']
    # 计算仿真过程中的实际产出
    sim_p = nmfd_instance.p(sim_n_c, sim_n_b)

    # 绘制轨迹散点
    ax.scatter(sim_n_c, sim_n_b, sim_p, c='r', marker='o', s=10, label='仿真轨迹点')

    # 设置图表样式
    ax.set_title('3D宏观基本图 (3D-MFD) 与仿真轨迹', fontsize=16)
    ax.set_xlabel('小汽车累积量 (n_c)')
    ax.set_ylabel('公交车累积量 (n_b)')
    ax.set_zlabel('网络总产出 (Production, veh*m/s)')
    ax.legend()

    print("3D图生成完毕。")
    plt.show()


# --- 4. 主程序: 设置和运行模拟 ---
if __name__ == '__main__':
    # --- 您的专属配置区 ---

    # 1. 填入您成功拟合的3D-NMFD参数
    nmfd_params = {
        'gamma_c': 8.1299, 'alpha_c': -0.0126, 'beta_c': -0.0259,
        'gamma_b': 8.5987, 'alpha_b': -0.0017, 'beta_b': -0.0881
    }

    # 2. 定义仿真基本参数
    sim_config = {
        'sim_time': 3600,  # 仿真总时长 (s)
        'dt': 1,  # 仿真步长 (s)
        'L_c': 1500,  # !!! 请根据您的路网估算小汽车平均行程 (m) !!!
        'L_b': 2500,  # !!! 请根据您的路网估算公交车平均行程 (m) !!!
    }

    # 3. 定义交通需求概况 (单位: veh/s)
    #    这是一个示例，模拟了高峰-平峰-高峰的过程。
    #    !!! 您可以替换为您自己的需求数据 !!!
    time_steps = int(sim_config['sim_time'] / sim_config['dt'])
    demand_c = np.zeros(time_steps)
    demand_b = np.zeros(time_steps)

    # 平峰期
    demand_c[0:1000] = 1.5
    demand_b[0:1000] = 0.05  # ~18辆/小时

    # 高峰期 (可能导致拥堵)
    demand_c[1000:2600] = 4.0
    demand_b[1000:2600] = 0.1  # ~36辆/小时

    # 平峰期
    demand_c[2600:] = 1.5
    demand_b[2600:] = 0.05

    sim_config['demand_c'] = demand_c
    sim_config['demand_b'] = demand_b

    # --- 运行仿真 ---
    nmfd_instance = NMFD(nmfd_params)
    simulator = HybridSimulator(nmfd_instance, **sim_config)
    results = simulator.run()

    # --- 绘制结果图 ---
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
    except:
        print("警告: 未找到'SimHei'字体，中文可能无法正常显示。")

    fig, axs = plt.subplots(3, 2, figsize=(15, 18), sharex=True)
    fig.suptitle('宏观混合模型仿真结果 (基于您的专属参数)', fontsize=16)

    time_axis = results['time']

    # 绘制累积量
    axs[0, 0].plot(time_axis, results['n_c'], label='小汽车累积量', color='blue')
    axs[0, 0].set_title('小汽车累积量 (Car Accumulation)')
    axs[0, 0].set_ylabel('车辆数 (veh)')
    axs[0, 0].grid(True)

    axs[0, 1].plot(time_axis, results['n_b'], label='公交车累积量', color='orange')
    axs[0, 1].set_title('公交车累积量 (Bus Accumulation)')
    axs[0, 1].grid(True)

    # 绘制入口流量
    axs[1, 0].plot(time_axis, results['f_c'], label='实际入口流量', color='blue')
    axs[1, 0].plot(time_axis, demand_c, label='需求', color='gray', alpha=0.5, linestyle=':')
    axs[1, 0].set_title('小汽车入口流量 (Car Inflow)')
    axs[1, 0].set_ylabel('流量 (veh/s)')
    axs[1, 0].legend()
    axs[1, 0].grid(True)

    axs[1, 1].plot(time_axis, results['f_b'], label='实际入口流量', color='orange')
    axs[1, 1].plot(time_axis, demand_b, label='需求', color='gray', alpha=0.5, linestyle=':')
    axs[1, 1].set_title('公交车入口流量 (Bus Inflow)')
    axs[1, 1].legend()
    axs[1, 1].grid(True)

    # 绘制出口流量
    axs[2, 0].plot(time_axis, results['G_c'], label='出口流量', color='blue')
    axs[2, 0].set_title('小汽车出口流量 (Car Outflow)')
    axs[2, 0].set_xlabel('时间 (s)')
    axs[2, 0].set_ylabel('流量 (veh/s)')
    axs[2, 0].grid(True)

    axs[2, 1].plot(time_axis, results['G_b'], label='出口流量', color='orange')
    axs[2, 1].set_title('公交车出口流量 (Bus Outflow)')
    axs[2, 1].set_xlabel('时间 (s)')
    axs[2, 1].grid(True)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    # --- 绘制新增的3D-MFD图 ---
    plot_3d_mfd(nmfd_instance, results)

