import traci
import pandas as pd
import sumolib
import random
import warnings
warnings.filterwarnings("ignore")
import matplotlib.pyplot as plt
import numpy as np
from scipy.interpolate import UnivariateSpline
from scipy.interpolate import make_interp_spline
from matplotlib.ticker import FuncFormatter

# 初始信号配时方案
signal_plan_before = pd.read_csv('初始信号配时方案1.csv')
before_trigger_time_list = signal_plan_before['Cycle'].unique().tolist()

# 连接sumo
sumo_binary = "C:/Program Files (x86)/Eclipse/Sumo/bin/sumo-gui.exe"
sumo_cfg_file = "NormalIntersection.sumocfg"
traci.start([sumo_binary, "-c", sumo_cfg_file])

# 全局phase(三号交叉口)定义
phase_dict = {'1': 'GrrrrGGGGrrGrrrGGGGrr',
              '2': 'GrrrrGGrrGGGrrrGrrrGG',
              '3': 'GGGrrGGrrrrGGGrGrrrrr',
              '4': 'GrrGGGGrrrrGrrGGrrrrr'}

before_phase_duration_dict = signal_plan_before.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

before_logic_dict = {}

for phase_time, duration_dict in before_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))

    before_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

# 初始信号配时开始仿真
signal_cycle = 90
person_num_bus = random.randint(40, 45)
person_num_car = 1.5
vehicle_tracking_before = []
current_time = 0
cycle_start_time = current_time

total_person_delay = 0
minute_delay = 0
last_minute = 0

phase_to_lane = {
    "1": ["phase3_1_2", "phase3_1_1", "phase3_3_1", "phase3_3_2", "phase3_3_3"],
    "2": ["phase3_1_3", "phase3_3_4"],
    "3": ["phase3_2_1", "phase3_2_2", "phase3_4_2", "phase3_4_1"],
    "4": ["phase3_4_3", "phase3_2_3"]
}

simulation_data = []

# while traci.simulation.getMinExpectedNumber() > 0:
while traci.simulation.getTime()<3600:
    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in before_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)
    # 切换配时方案
    if current_time in before_trigger_time_list:
        traci.trafficlight.setProgram('light3', str(current_time))
    # 开始仿真
    traci.simulationStep()
    current_time += 1

    current_minute = current_time // 90
    if current_minute != last_minute:
        # 保存上一分钟的延误时间
        simulation_data.append((current_minute, minute_delay))
        # 重置延误时间
        minute_delay = 0
        last_minute = current_minute

    # 开始仿真计算每秒交叉口等待车辆数
    for phase_name, lane_name in phase_to_lane.items():
        for lane in lane_name:
            vehicle_list = traci.lane.getLastStepVehicleIDs(lane)
            for vehicle_id in vehicle_list:
                vehicle_type = traci.vehicle.getTypeID(vehicle_id)
                vehicle_speed = traci.vehicle.getSpeed(vehicle_id)
                if vehicle_id not in vehicle_tracking_before and vehicle_speed < 0.1:
                    vehicle_wait_time = traci.lane.getWaitingTime(lane)
                    if vehicle_type == "car":
                        total_person_delay_car = vehicle_wait_time * person_num_car
                        total_person_delay += total_person_delay_car
                        minute_delay += total_person_delay_car
                        vehicle_tracking_before.append(vehicle_id)
                    elif vehicle_type == "bus":
                        total_person_delay_bus = vehicle_wait_time * person_num_bus
                        total_person_delay += total_person_delay_bus
                        minute_delay += total_person_delay_bus
                        vehicle_tracking_before.append(vehicle_id)
                elif vehicle_id in vehicle_tracking_before and vehicle_speed > 0.1:
                    # 当车辆速度不为0时，清零延误时间
                    vehicle_tracking_before.remove(vehicle_id)

# 关闭仿真
traci.close()

# 优化后的信号配时方案
signal_plan_after = pd.read_csv('优化后的信号配时方案1.csv')
after_trigger_time_list = signal_plan_after['Cycle'].unique().tolist()

traci.start([sumo_binary, "-c", sumo_cfg_file])

after_phase_duration_dict = signal_plan_after.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

after_logic_dict = {}

for phase_time, duration_dict in after_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))

    after_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})


vehicle_tracking_after = []
current_time = 0
cycle_start_time = current_time

total_person_delay_after = 0
minute_delay_after = 0
last_minute_after = 0
simulation_data_after = []

# while traci.simulation.getMinExpectedNumber() > 0:
while traci.simulation.getTime()<3600:
    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in after_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)
    # 切换配时方案
    if current_time in after_trigger_time_list:
        traci.trafficlight.setProgram('light3', str(current_time))
    # 开始仿真
    traci.simulationStep()
    current_time += 1
    # 检查是否已经过了一分钟
    current_minute = current_time // 90
    if current_minute != last_minute_after:
        # 保存上一分钟的延误时间
        simulation_data_after.append((current_minute, minute_delay_after))
        # 重置延误时间
        minute_delay_after = 0
        last_minute_after = current_minute
    # 开始仿真计算每秒交叉口等待车辆数
    for phase_name, lane_name in phase_to_lane.items():
        for lane in lane_name:
            vehicle_list = traci.lane.getLastStepVehicleIDs(lane)
            for vehicle_id in vehicle_list:
                vehicle_type = traci.vehicle.getTypeID(vehicle_id)
                vehicle_speed = traci.vehicle.getSpeed(vehicle_id)
                if vehicle_id not in vehicle_tracking_after and vehicle_speed < 0.1:
                    vehicle_wait_time = traci.lane.getWaitingTime(lane)
                    if vehicle_type == "car":
                        total_person_delay_car = vehicle_wait_time * person_num_car
                        total_person_delay_after += total_person_delay_car
                        minute_delay_after += total_person_delay_car
                        vehicle_tracking_after.append(vehicle_id)
                    elif vehicle_type == "bus":
                        total_person_delay_bus = vehicle_wait_time * person_num_bus
                        total_person_delay_after += total_person_delay_bus
                        minute_delay_after += total_person_delay_bus
                        vehicle_tracking_after.append(vehicle_id)
                elif vehicle_id in vehicle_tracking_after and vehicle_speed > 0.1:
                    # 当车辆速度不为0时，清零延误时间
                    vehicle_tracking_after.remove(vehicle_id)

# 关闭仿真
traci.close()



# # 原始数据
# x_values_before = np.array([data[0] for data in simulation_data])
# y_values_before = np.array([data[1] for data in simulation_data])
#
# x_values_after = np.array([data[0] for data in simulation_data_after])
# y_values_after = np.array([data[1] for data in simulation_data_after])
#
# # 进行二次多项式拟合
# p = np.polyfit(x_values_before, np.log(y_values_before), 3)
# p_after = np.polyfit(x_values_after, np.log(y_values_after), 3)
#
# y_fit_before = np.exp(np.polyval(p, x_values_before))
# y_fit_after = np.exp(np.polyval(p_after, x_values_after))
#
# # 绘制拟合曲线
# plt.figure(figsize=(10, 6))
#
# plt.plot(x_values_before, y_fit_before, label='Before Optimization', color='blue')
# plt.plot(x_values_after, y_fit_after, label='After Optimization', color='orange')
# plt.xlabel('Simulation Time (minutes)')
# plt.ylabel('Total Delay Time')
# plt.title('Smoothed Total Delay Over Time')
# plt.yscale('log')
# plt.legend()
#
# plt.show()


# 原始数据
x_values_before = np.array([data[0] for data in simulation_data])
y_values_before = np.array([data[1] for data in simulation_data])

x_values_after = np.array([data[0] for data in simulation_data_after])
y_values_after = np.array([data[1] for data in simulation_data_after])

# 绘制原始数据曲线
plt.figure(figsize=(10, 6))
plt.plot(x_values_before, y_values_before, label='Before Optimization', color='blue', linestyle='--')
plt.plot(x_values_after, y_values_after, label='After Optimization', color='orange', linestyle='--')
plt.xlabel('Simulation Time (minutes)')
plt.ylabel('Total Delay Time')
plt.title('Smoothed Total Delay Over Time')
plt.yscale('log')
plt.legend()

plt.show()

