
import random
import csv
import traci
import warnings
import matplotlib.pyplot as plt

warnings.filterwarnings("ignore")

# 连接sumo
sumo_binary = "C:/Program Files (x86)/Eclipse/Sumo/bin/sumo-gui.exe"
sumo_cfg_file = "2.sumocfg"
traci.start([sumo_binary, "-c", sumo_cfg_file])

simulation_data = []
current_time = 0
bus_number = 0
car_number = 0
last_minute = 0
person_bus = random.randint(35,40)
person_car = 2
total_person = 0
# while traci.simulation.getMinExpectedNumber() > 0 :
while traci.simulation.getTime() < 6400:

    # 开始仿真
    traci.simulationStep()
    current_time += 1
    current_minute = current_time // 90

    # 每分钟结束时收集数据
    if current_minute != last_minute:
        total_person = bus_number*person_bus + car_number*person_car
        simulation_data.append((current_minute, bus_number, car_number,total_person))
        bus_number = 0
        car_number = 0
        total_person = 0
        last_minute = current_minute

    # 开始仿真计算每秒交叉口等待车辆数
    lane_id = ["phase3_1", "phase3_2", "phase3_3", "phase3_4"]

    for i in lane_id:
        vehicle_id = traci.edge.getLastStepVehicleIDs(i)
        for j in vehicle_id:
            vehicle_type = traci.vehicle.getTypeID(j)
            if vehicle_type == "bus":
                bus_number += 1
            elif vehicle_type == "car":
                car_number += 1

# 关闭仿真
traci.close()

# 绘制图表
minutes = [data[0] for data in simulation_data]
bus_counts = [data[1] for data in simulation_data]
car_counts = [data[2] for data in simulation_data]
# person_count = [data[3] for data in simulation_data]

# 文件名
csv_filename = "simulation_data.csv"

# 列标题
headers = ["Minutes", "Bus Counts", "Car Counts"]

# 将数据写入 CSV 文件
with open(csv_filename, mode='w', newline='') as file:
    writer = csv.writer(file)
    writer.writerow(headers)
    writer.writerows(zip(minutes, bus_counts, car_counts))

print("CSV 文件已生成:", csv_filename)


plt.figure(figsize=(10, 6))
plt.plot(minutes, bus_counts, label='Bus')
plt.plot(minutes, car_counts, label='Car')
# plt.plot(minutes,person_count,label='person')
plt.xlabel('cycle')
plt.ylabel('Vehicle Count')
plt.title('Number of Vehicles and Person Per Minute')
plt.legend()
plt.grid(True)
plt.show()
