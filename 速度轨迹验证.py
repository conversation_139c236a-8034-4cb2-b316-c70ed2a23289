import time
import traci
import pandas as pd
import sumolib
import random
import warnings
warnings.filterwarnings("ignore")
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.font_manager import FontProperties
font = FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf", size=10)


"""单单优化信号"""
# 初始信号配时方案
signal_plan_before = pd.read_csv('初始信号配时方案1.csv')
before_trigger_time_list = signal_plan_before['Cycle'].unique().tolist()

# 连接sumo
sumo_binary = "C:/Program Files (x86)/Eclipse/Sumo/bin/sumo-gui.exe"
sumo_cfg_file = "NormalIntersection10%.sumocfg"
traci.start([sumo_binary, "-c", sumo_cfg_file])

# 全局phase(三号交叉口)定义
phase_dict = {'1': 'GrrrrGGGGrrGrrrGGGGrr',
              '2': 'GrrrrGGrrGGGrrrGrrrGG',
              '3': 'GGGrrGGrrrrGGGrGrrrrr',
              '4': 'GrrGGGGrrrrGrrGGrrrrr'}

junction_id3 = "light3"

before_phase_duration_dict = signal_plan_before.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

before_logic_dict = {}

for phase_time, duration_dict in before_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))

    before_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#初始信号配时开始仿真
bus_max_speed=14
bus_min_speed=4
signal_cycle=90
cycle_counter=1
person_num_bus=random.randint(35,40)
person_num_car=2
vehicle_tracking_before=[]
track_cycle = []

current_time = 0
cycle_start_time=current_time

car_count_before = 0
bus_count_before = 0

cycles_before=[]
car_in_cycle_before=0
bus_in_cycle_before=0
person_in_cycle_before=0

next_phase_time=0
# 被控公交列表
controled_bus=[]

#公交减速点字典
Dec_point={}

phase_to_lane={
    "1":["phase3_1_2","phase3_1_1","phase3_3_1","phase3_3_2","phase3_3_3"],
    "2":["phase3_1_3","phase3_3_4"],
    "3":["phase3_2_1","phase3_2_2","phase3_4_2","phase3_4_1"],
    "4":["phase3_4_3","phase3_2_3"]
}

while traci.simulation.getMinExpectedNumber() > 0:
    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in before_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)
    # 切换配时方案
    if current_time in before_trigger_time_list:
        traci.trafficlight.setProgram('light3', str(current_time))
    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_before and vehicle_speed<0.1:
                        vehicle_wait_time = traci.edge.getWaitingTime(lane)
                        if vehicle_type=="car":
                            car_count_before += vehicle_wait_time
                            vehicle_tracking_before.append(vehicle_id)

                        elif vehicle_type=="bus":
                            bus_count_before += vehicle_wait_time
                            vehicle_tracking_before.append(vehicle_id)

    if current_time > time_interval_end:

        car_count_cycle_before = car_count_before
        bus_count_cycle_before = bus_count_before
        person_count_cycle_before = car_count_before + bus_count_before
        print(f"Cycle {cycle_counter}: 社会车等待总时间={car_count_cycle_before}, 公交车等待总时间={bus_count_cycle_before}")
        print(f"Cycle {cycle_counter}: 等待总时间={person_count_cycle_before}")

        car_count_before = 0
        bus_count_before = 0
        cycle_start_time = time_interval_end

        #统计周期结束时的车辆数量
        # car_count_cycle_before=len(car_count_before)
        # bus_count_cycle_before=len(bus_count_before)
        # person_count_cycle_before=int(car_count_cycle_before*person_num_car+bus_count_cycle_before*person_num_bus)
        #
        # print(f"Cycle {cycle_counter}: Cars={car_count_cycle_before}, Buses={bus_count_cycle_before}")
        # print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_before}")
        #
        # cycles_before.append(cycle_counter)
        # car_in_cycle_before.append(car_count_cycle_before)
        # bus_in_cycle_before.append(bus_count_cycle_before)
        # person_in_cycle_before.append(person_count_cycle_before)
        #
        # car_count_before = []
        # bus_count_before = []
        #
        # cycle_start_time = time_interval_end
        # cycle_counter += 1

traci.close()

#相位剩余时间
def phase_remain_time(tls_id):
    global remain_time
    simulation_time=traci.simulation.getTime()
    next_switch_time=traci.trafficlight.getNextSwitch(tls_id)
    phase_id=traci.trafficlight.getPhase(tls_id)
    remain_time=next_switch_time-simulation_time
    return remain_time

#获取信号灯各相位持续时间
def get_duration_time(tls_id):
    tl_def = traci.trafficlight.getAllProgramLogics(tls_id)
    program = traci.trafficlight.getProgram(tls_id)
    obj_logic = [_i for _i in tl_def if _i.getSubID() == program][0]
    phase_duration = []
    for phase in obj_logic.phases:
        a=phase.duration
        phase_duration.append(a)
    return phase_duration

#获取下周期绿灯时间
def next_phase_green_time(tls,a,b):
    if b < a:
        result=sum(tls[b:a])
    else:
        result=sum(tls[b:])+sum(tls[:a])
    return result

#轨迹推算1
def trajectory_estimation1(lane1_id,bus_id,next_phase_time):

    #获取车辆在路段上的剩余位置
    remain_distance = traci.lane.getLength(lane1_id) - traci.vehicle.getLanePosition(bus_id)
    if remain_time != 0:
        predict_distance = remain_time*traci.vehicle.getSpeed(bus_id)
        predict_distance_max = remain_time * bus_max_speed
        if predict_distance >= remain_distance or predict_distance_max >= remain_distance:
            traci.vehicle.setSpeed(bus_id,bus_max_speed)
        else:
            #计算减速点
            dec_point = (remain_time + signal_cycle) * bus_min_speed
            Dec_point[bus_id] = dec_point

    else:
        predict_distance = next_phase_time * traci.vehicle.getSpeed(bus_id)
        if predict_distance <= remain_distance:
            traci.vehicle.setSpeed(bus_id,bus_min_speed)
        else:
            #计算减速点
            dec_point = next_phase_time*bus_min_speed
            Dec_point[bus_id] = dec_point

    return Dec_point

#轨迹推算1
def trajectory_estimation2(lane1_id,bus_id,next_phase_time):
    #获取车辆在路段上的剩余位置
    remain_distance = traci.lane.getLength(lane1_id) - traci.vehicle.getLanePosition(bus_id)
    if remain_time != 0:
        predict_distance = remain_time*traci.vehicle.getSpeed(bus_id)
        predict_distance_max = remain_time * bus_max_speed
        if predict_distance >= remain_distance or predict_distance_max >= remain_distance:
            pass
        else:
            #计算减速点
            dec_point = (remain_time + next_phase_time) * bus_min_speed
            Dec_point[bus_id] = dec_point

    else:
        predict_distance = next_phase_time * traci.vehicle.getSpeed(bus_id)
        if predict_distance <= remain_distance:
            pass
        else:
            #计算减速点
            dec_point = next_phase_time*bus_min_speed
            Dec_point[bus_id] = dec_point

    return Dec_point

#公交速度控制
def speed_control(dec_point,bus_id,lane1_id,remain_time):
    #路段长度
    road_length1=traci.lane.getLength(lane1_id)
    #距离路口的距离
    remain_distance = road_length1- traci.vehicle.getLanePosition(bus_id)
    if dec_point is None:
        traci.vehicle.setSpeed(bus_id,bus_min_speed)
    elif dec_point <= road_length1:
        if road_length1 - traci.vehicle.getLanePosition(bus_id) <= dec_point:
            traci.vehicle.setSpeed(bus_id,bus_min_speed)
    else:
        dec_point2=dec_point-traci.lane.getLength(lane1_id)
        if road_length1-traci.vehicle.getLanePosition(bus_id) > dec_point2:
            traci.vehicle.setSpeed(bus_id,bus_max_speed)

"""单单速度控制"""
# 配时方案
signal_plan_after = pd.read_csv('初始信号配时方案1.csv')
after_trigger_time_list = signal_plan_after['Cycle'].unique().tolist()

traci.start([sumo_binary, "-c", sumo_cfg_file])

after_phase_duration_dict = signal_plan_after.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()

after_logic_dict = {}

for phase_time, duration_dict in after_phase_duration_dict.items():
    phase_list = []
    for phase, state in phase_dict.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))

    after_logic_dict.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#优化信号配时方案开始仿真
vehicle_tracking_after=[]
cycle_counter=1
current_time = 0
cycle_start_time=current_time

car_count_after = 0
bus_count_after = 0

cycles_after=[]
car_in_cycle_after=0
bus_in_cycle_after=0
person_in_cycle_after=0

while traci.simulation.getMinExpectedNumber() > 0:
    cycle_counter = current_time//signal_cycle + 1

    # 仿真开始前输入信号配时方案
    if current_time == 0:
        # global phase_time
        for phase_time, logic in after_logic_dict.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案（每周期切换一次）
    if cycle_counter in after_trigger_time_list:
        if cycle_counter not in track_cycle:
            traci.trafficlight.setProgram('light3', str(cycle_counter))
            track_cycle.append(cycle_counter)


    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #优先相位剩余绿灯时间计算(0相位)
    phase_duration = get_duration_time(junction_id3)
    # print(f"周期{cycle_counter}信号配时方案：{phase_duration}")
    # 获取优先相位剩余绿灯时间（0相位）
    if traci.trafficlight.getPhase(junction_id3) == 0:
        remain_time = phase_remain_time(junction_id3)
        print(f"优先相位剩余绿灯时间:{remain_time}")

    else:
        phase = traci.trafficlight.getPhase(junction_id3)
        remain_time2 = phase_remain_time(junction_id3)
        remain_time = 0
        next_phase_time = next_phase_green_time(phase_duration,0,phase) - phase_duration[phase] + remain_time2
        print(f"下一相位切换时间:{next_phase_time}")

    #获取优先相位方向路段上的所有车辆id （0相位）
    vehicle_ids = traci.edge.getLastStepVehicleIDs("phase3_1") + traci.edge.getLastStepVehicleIDs("phase3_3")
    #遍历每个车辆id，将公交车筛选出来（0相位）
    for vehicle_id in vehicle_ids:
        vehicle_type = traci.vehicle.getTypeID(vehicle_id)
        if vehicle_type =="bus":
            if vehicle_id not in controled_bus:
                orginal_lane = traci.vehicle.getLaneID(vehicle_id)
                Dec_point = trajectory_estimation1(orginal_lane,vehicle_id,next_phase_time)
                controled_bus.append(vehicle_id)

        #南进口道直行
        vehicle_list = traci.lane.getLastStepVehicleIDs('phase3_1_2') + traci.lane.getLastStepVehicleIDs('phase3_1_1')
        for i in vehicle_list:
            bus_type = traci.vehicle.getTypeID(i)
            if remain_time != 0 and bus_type == "bus":
                remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i)

                diatance_max = remain_time * bus_max_speed
                diatance_min = remain_time * bus_min_speed


                if diatance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i,bus_max_speed)

                elif diatance_min <= remain_diatance <= diatance_max:
                    traci.vehicle.setSpeed(i,bus_max_speed)

                else:
                    traci.vehicle.setSpeed(i,bus_min_speed)

            elif remain_time == 0 and bus_type == "bus":

                remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i)
                distance_max = bus_max_speed * next_phase_time
                distance_min = bus_min_speed * next_phase_time

                if distance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i,bus_min_speed)

                elif distance_min <= remain_diatance <= distance_max:
                    traci.vehicle.setSpeed(i,bus_min_speed)

                elif distance_max <= remain_diatance:
                    traci.vehicle.setSpeed(i,bus_max_speed)


        #北进口道直行
        vehicle_list = traci.lane.getLastStepVehicleIDs('phase3_3_1') + traci.lane.getLastStepVehicleIDs('phase3_3_2')+ traci.lane.getLastStepVehicleIDs('phase3_3_3')
        for i in vehicle_list:
            bus_type = traci.vehicle.getTypeID(i)
            if remain_time != 0 and bus_type == "bus":
                #任意一条边计算道路长度
                remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i)
                diatance_max = remain_time * bus_max_speed
                diatance_min = remain_time * bus_min_speed

                if diatance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                elif diatance_min <= remain_diatance <= diatance_max:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                else:
                    traci.vehicle.setSpeed(i, bus_min_speed)
            elif remain_time == 0 and bus_type == "bus":
                # 当前相位为红灯，减速行驶
                remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i)
                distance_max = bus_max_speed * next_phase_time
                distance_min = bus_min_speed * next_phase_time

                if distance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_min_speed)

                elif distance_min <= remain_diatance <= distance_max:
                    traci.vehicle.setSpeed(i, bus_min_speed)

                elif distance_max <= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)



    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真计算每秒交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        for phase_name,lane_name in phase_to_lane.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:

                    vehicle_type = traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed = traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_after and vehicle_speed < 0.1:
                        vehicle_wait_time = traci.edge.getWaitingTime(lane)
                        if vehicle_type=="car":

                            car_count_after += vehicle_wait_time
                            vehicle_tracking_after.append(vehicle_id)

                        elif vehicle_type=="bus":
                            bus_count_after += vehicle_wait_time
                            vehicle_tracking_after.append(vehicle_id)

    if current_time > time_interval_end:

        #统计周期结束后的车辆等待时间
        car_count_cycle_after = car_count_after
        bus_count_cycle_after = bus_count_after
        person_count_cycle_after = car_count_after + bus_count_after
        print(f"Cycle {cycle_counter}: 社会车等待总时间={car_count_cycle_after}, 公交车等待总时间={bus_count_cycle_after}")
        print(f"Cycle {cycle_counter}: 等待总时间={person_count_cycle_after}")

        car_count_after = 0
        bus_count_after = 0
        cycle_start_time = time_interval_end





        #统计周期结束时的车辆数量
        # car_count_cycle_after = len(car_count_after)
        # bus_count_cycle_after = len(bus_count_after)
        # person_count_cycle_after = int(car_count_cycle_after*person_num_car + bus_count_cycle_after*person_num_bus)
        #
        # print(f"Cycle {cycle_counter}: Cars={car_count_cycle_after}, Buses={bus_count_cycle_after}")
        # print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_after}")
        #
        # cycles_after.append(cycle_counter)
        # car_in_cycle_after.append(car_count_cycle_after)
        # bus_in_cycle_after.append(bus_count_cycle_after)
        # person_in_cycle_after.append(person_count_cycle_after)
        #
        # car_count_after = []
        # bus_count_after = []
        # cycle_start_time = time_interval_end





traci.close()

# 数据
# cycles = cycles_before[:45]
# car_before = car_in_cycle_before[:45]
# bus_before = bus_in_cycle_before[:45]
# person_before = person_in_cycle_before[:45]
# car_after = car_in_cycle_after[:45]
# bus_after = bus_in_cycle_after[:45]
# person_after = person_in_cycle_after[:45]

cycles = cycles_before[:45]
car_before = car_in_cycle_before[:45]
bus_before = bus_in_cycle_before[:45]
person_before = person_in_cycle_before[:45]
car_after = car_in_cycle_after[:45]
bus_after = bus_in_cycle_after[:45]
person_after = person_in_cycle_after[:45]

# 创建雷达图
labels = cycles

# 公交车数量
angles = np.linspace(0, 2*np.pi, len(labels), endpoint=False).tolist()
stats_bus_before = bus_before
stats_bus_after = bus_after

fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(polar=True))
ax.fill(angles, stats_bus_before, color='blue', alpha=0.25, label='速度控制前')
ax.fill(angles, stats_bus_after, color='green', alpha=0.25, label='速度控制')

ax.set_yticklabels(["{:.0f}".format(i) for i in ax.get_yticks()])  # 添加公交车数值刻度
ax.set_xticks(angles)
ax.set_xticklabels(labels)

plt.title('公交渗透率10%下信号优化与速度控制公交车延误对比', fontproperties=font)
plt.legend(loc='upper right', prop=font)

plt.show()

# 社会车数量
stats_car_before = car_before
stats_car_after = car_after

fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(polar=True))
ax.fill(angles, stats_car_before, color='blue', alpha=0.25, label='速度控制后')
ax.fill(angles, stats_car_after, color='green', alpha=0.25, label='速度控制')

ax.set_yticklabels(["{:.0f}".format(i) for i in ax.get_yticks()])  # 添加社会车数值刻度
ax.set_xticks(angles)
ax.set_xticklabels(labels)

plt.title('公交渗透率10%下信号优化与速度控制社会车延误对比', fontproperties=font)
plt.legend(loc='upper right', prop=font)

plt.show()

# 延误人数
stats_person_before = person_before
stats_person_after = person_after

fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(polar=True))
ax.fill(angles, stats_person_before, color='blue', alpha=0.25, label='速度控制前')
ax.fill(angles, stats_person_after, color='green', alpha=0.25, label='速度控制')

ax.set_yticklabels(["{:.0f}".format(i) for i in ax.get_yticks()])  # 添加延误人数数值刻度
ax.set_xticks(angles)
ax.set_xticklabels(labels)

plt.title('公交渗透率10%下信号优化与速度控制延误人数对比', fontproperties=font)
plt.legend(loc='upper right', prop=font)

plt.show()