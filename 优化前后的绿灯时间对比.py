import pandas as pd
import matplotlib.pyplot as plt

# 读取优化前的CSV文件
df_before = pd.read_csv('初始信号配时方案1.csv')

# 读取优化后的CSV文件
df_after = pd.read_csv('优化后的信号配时方案1.csv')

# 合并数据
df_before['Group'] = 'Before Optimization'
df_after['Group'] = 'After Optimization'
df = pd.concat([df_before, df_after])

# 获取唯一的周期数
cycles = df['Cycle'].unique()

# 设置图形尺寸
plt.figure(figsize=(20, 12))

# 设置柱子宽度
width = 0.25

# 遍历周期
for i, cycle in enumerate(cycles):
    # 获取该周期下的所有相位的绿灯时间
    data_before = df[(df['Cycle'] == cycle) & (df['Group'] == 'Before Optimization')].sort_values(by='Phase')
    data_after = df[(df['Cycle'] == cycle) & (df['Group'] == 'After Optimization')].sort_values(by='Phase')

    # 计算每个相位的绿灯时间
    green_times_before = data_before['Green_Light_Time'].tolist()
    green_times_after = data_after['Green_Light_Time'].tolist()

    # 绘制柱子
    # 第一个相位为绿色，其他相位为红色
    plt.barh(i, green_times_before[0], height=width, color='green', label='Before Optimization' if i == 0 else None)
    plt.barh(i, sum(green_times_before[1:]), left=green_times_before[0], height=width, color='red')

    plt.barh(i + width, green_times_after[0], height=width, color='green',
             label='After Optimization' if i == 0 else None)
    plt.barh(i + width, sum(green_times_after[1:]), left=green_times_after[0], height=width, color='red')


# 添加图例
plt.legend()

# 设置坐标轴标签和标题
plt.ylabel('Cycle')
plt.xlabel('Green Light Time')
plt.title('Green Light Time by Cycle and Phase')

# 设置y轴刻度
plt.yticks(range(len(cycles)), cycles)

# 显示图形
plt.show()
