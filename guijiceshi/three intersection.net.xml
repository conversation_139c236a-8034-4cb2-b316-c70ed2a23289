<?xml version="1.0" encoding="UTF-8"?>

<!-- generated on 2024-10-27 15:52:19 by Eclipse SUMO netedit Version 1.18.0
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://sumo.dlr.de/xsd/neteditConfiguration.xsd">

    <input>
        <sumo-net-file value="C:\Users\<USER>\Desktop\毕业\TSP\SUMO仿真验证\guijiceshi\three intersection.net.xml"/>
    </input>

    <output>
        <output-file value="C:\Users\<USER>\Desktop\毕业\TSP\SUMO仿真验证\guijiceshi\three intersection.net.xml"/>
    </output>

    <processing>
        <geometry.min-radius.fix.railways value="false"/>
        <geometry.max-grade.fix value="false"/>
        <offset.disable-normalization value="true"/>
        <lefthand value="0"/>
    </processing>

    <junctions>
        <no-turnarounds value="true"/>
        <junctions.corner-detail value="5"/>
        <junctions.limit-turn-speed value="5.50"/>
        <rectangular-lane-cut value="0"/>
    </junctions>

    <pedestrian>
        <walkingareas value="0"/>
    </pedestrian>

</configuration>
-->

<net version="1.16" junctionCornerDetail="5" limitTurnSpeed="5.50" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://sumo.dlr.de/xsd/net_file.xsd">

    <location netOffset="0.00,0.00" convBoundary="-900.00,-300.00,900.00,300.00" origBoundary="-10000000000.00,-10000000000.00,10000000000.00,10000000000.00" projParameter="!"/>

    <edge id=":J1_0" function="internal">
        <lane id=":J1_0_0" index="0" speed="6.51" length="9.03" shape="438.80,16.80 438.45,14.35 437.40,12.60 435.65,11.55 433.20,11.20"/>
    </edge>
    <edge id=":J1_1" function="internal">
        <lane id=":J1_1_0" index="0" speed="13.89" length="33.60" shape="442.00,16.80 442.00,-16.80"/>
        <lane id=":J1_1_1" index="1" speed="13.89" length="33.60" shape="445.20,16.80 445.20,-16.80"/>
    </edge>
    <edge id=":J1_3" function="internal">
        <lane id=":J1_3_0" index="0" speed="11.36" length="29.67" shape="448.40,16.80 449.55,8.75 453.00,3.00 458.75,-0.45 466.80,-1.60"/>
    </edge>
    <edge id=":J1_4" function="internal">
        <lane id=":J1_4_0" index="0" speed="6.51" length="9.03" shape="466.80,11.20 464.35,11.55 462.60,12.60 461.55,14.35 461.20,16.80"/>
    </edge>
    <edge id=":J1_5" function="internal">
        <lane id=":J1_5_0" index="0" speed="13.89" length="33.60" shape="466.80,8.00 433.20,8.00"/>
        <lane id=":J1_5_1" index="1" speed="13.89" length="33.60" shape="466.80,4.80 433.20,4.80"/>
    </edge>
    <edge id=":J1_7" function="internal">
        <lane id=":J1_7_0" index="0" speed="11.36" length="29.67" shape="466.80,1.60 458.75,0.45 453.00,-3.00 449.55,-8.75 448.40,-16.80"/>
    </edge>
    <edge id=":J1_8" function="internal">
        <lane id=":J1_8_0" index="0" speed="6.51" length="9.03" shape="461.20,-16.80 461.55,-14.35 462.60,-12.60 464.35,-11.55 466.80,-11.20"/>
    </edge>
    <edge id=":J1_9" function="internal">
        <lane id=":J1_9_0" index="0" speed="13.89" length="33.60" shape="458.00,-16.80 458.00,16.80"/>
        <lane id=":J1_9_1" index="1" speed="13.89" length="33.60" shape="454.80,-16.80 454.80,16.80"/>
    </edge>
    <edge id=":J1_11" function="internal">
        <lane id=":J1_11_0" index="0" speed="11.36" length="29.67" shape="451.60,-16.80 450.45,-8.75 447.00,-3.00 441.25,0.45 433.20,1.60"/>
    </edge>
    <edge id=":J1_12" function="internal">
        <lane id=":J1_12_0" index="0" speed="6.51" length="9.03" shape="433.20,-11.20 435.65,-11.55 437.40,-12.60 438.45,-14.35 438.80,-16.80"/>
    </edge>
    <edge id=":J1_13" function="internal">
        <lane id=":J1_13_0" index="0" speed="13.89" length="33.60" shape="433.20,-8.00 466.80,-8.00"/>
        <lane id=":J1_13_1" index="1" speed="13.89" length="33.60" shape="433.20,-4.80 466.80,-4.80"/>
    </edge>
    <edge id=":J1_15" function="internal">
        <lane id=":J1_15_0" index="0" speed="11.36" length="29.67" shape="433.20,-1.60 441.25,-0.45 447.00,3.00 450.45,8.75 451.60,16.80"/>
    </edge>
    <edge id=":intersection1_0" function="internal">
        <lane id=":intersection1_0_0" index="0" speed="6.51" length="9.03" shape="-461.20,16.80 -461.55,14.35 -462.60,12.60 -464.35,11.55 -466.80,11.20"/>
    </edge>
    <edge id=":intersection1_1" function="internal">
        <lane id=":intersection1_1_0" index="0" speed="13.89" length="33.60" shape="-458.00,16.80 -458.00,-16.80"/>
        <lane id=":intersection1_1_1" index="1" speed="13.89" length="33.60" shape="-454.80,16.80 -454.80,-16.80"/>
    </edge>
    <edge id=":intersection1_3" function="internal">
        <lane id=":intersection1_3_0" index="0" speed="11.36" length="29.67" shape="-451.60,16.80 -450.45,8.75 -447.00,3.00 -441.25,-0.45 -433.20,-1.60"/>
    </edge>
    <edge id=":intersection1_4" function="internal">
        <lane id=":intersection1_4_0" index="0" speed="6.51" length="9.03" shape="-433.20,11.20 -435.65,11.55 -437.40,12.60 -438.45,14.35 -438.80,16.80"/>
    </edge>
    <edge id=":intersection1_5" function="internal">
        <lane id=":intersection1_5_0" index="0" speed="13.89" length="33.60" shape="-433.20,8.00 -466.80,8.00"/>
        <lane id=":intersection1_5_1" index="1" speed="13.89" length="33.60" shape="-433.20,4.80 -466.80,4.80"/>
    </edge>
    <edge id=":intersection1_7" function="internal">
        <lane id=":intersection1_7_0" index="0" speed="11.36" length="29.67" shape="-433.20,1.60 -441.25,0.45 -447.00,-3.00 -450.45,-8.75 -451.60,-16.80"/>
    </edge>
    <edge id=":intersection1_8" function="internal">
        <lane id=":intersection1_8_0" index="0" speed="6.51" length="9.03" shape="-438.80,-16.80 -438.45,-14.35 -437.40,-12.60 -435.65,-11.55 -433.20,-11.20"/>
    </edge>
    <edge id=":intersection1_9" function="internal">
        <lane id=":intersection1_9_0" index="0" speed="13.89" length="33.60" shape="-442.00,-16.80 -442.00,16.80"/>
        <lane id=":intersection1_9_1" index="1" speed="13.89" length="33.60" shape="-445.20,-16.80 -445.20,16.80"/>
    </edge>
    <edge id=":intersection1_11" function="internal">
        <lane id=":intersection1_11_0" index="0" speed="11.36" length="29.67" shape="-448.40,-16.80 -449.55,-8.75 -453.00,-3.00 -458.75,0.45 -466.80,1.60"/>
    </edge>
    <edge id=":intersection1_12" function="internal">
        <lane id=":intersection1_12_0" index="0" speed="6.51" length="9.03" shape="-466.80,-11.20 -464.35,-11.55 -462.60,-12.60 -461.55,-14.35 -461.20,-16.80"/>
    </edge>
    <edge id=":intersection1_13" function="internal">
        <lane id=":intersection1_13_0" index="0" speed="13.89" length="33.60" shape="-466.80,-8.00 -433.20,-8.00"/>
        <lane id=":intersection1_13_1" index="1" speed="13.89" length="33.60" shape="-466.80,-4.80 -433.20,-4.80"/>
    </edge>
    <edge id=":intersection1_15" function="internal">
        <lane id=":intersection1_15_0" index="0" speed="11.36" length="29.67" shape="-466.80,-1.60 -458.75,-0.45 -453.00,3.00 -449.55,8.75 -448.40,16.80"/>
    </edge>
    <edge id=":intersection2_0" function="internal">
        <lane id=":intersection2_0_0" index="0" speed="6.51" length="9.03" shape="-11.20,16.80 -11.55,14.35 -12.60,12.60 -14.35,11.55 -16.80,11.20"/>
    </edge>
    <edge id=":intersection2_1" function="internal">
        <lane id=":intersection2_1_0" index="0" speed="13.89" length="33.60" shape="-8.00,16.80 -8.00,-16.80"/>
        <lane id=":intersection2_1_1" index="1" speed="13.89" length="33.60" shape="-4.80,16.80 -4.80,-16.80"/>
    </edge>
    <edge id=":intersection2_3" function="internal">
        <lane id=":intersection2_3_0" index="0" speed="11.36" length="29.67" shape="-1.60,16.80 -0.45,8.75 3.00,3.00 8.75,-0.45 16.80,-1.60"/>
    </edge>
    <edge id=":intersection2_4" function="internal">
        <lane id=":intersection2_4_0" index="0" speed="6.51" length="9.03" shape="16.80,11.20 14.35,11.55 12.60,12.60 11.55,14.35 11.20,16.80"/>
    </edge>
    <edge id=":intersection2_5" function="internal">
        <lane id=":intersection2_5_0" index="0" speed="13.89" length="33.60" shape="16.80,8.00 -16.80,8.00"/>
        <lane id=":intersection2_5_1" index="1" speed="13.89" length="33.60" shape="16.80,4.80 -16.80,4.80"/>
    </edge>
    <edge id=":intersection2_7" function="internal">
        <lane id=":intersection2_7_0" index="0" speed="11.36" length="29.67" shape="16.80,1.60 8.75,0.45 3.00,-3.00 -0.45,-8.75 -1.60,-16.80"/>
    </edge>
    <edge id=":intersection2_8" function="internal">
        <lane id=":intersection2_8_0" index="0" speed="6.51" length="9.03" shape="11.20,-16.80 11.55,-14.35 12.60,-12.60 14.35,-11.55 16.80,-11.20"/>
    </edge>
    <edge id=":intersection2_9" function="internal">
        <lane id=":intersection2_9_0" index="0" speed="13.89" length="33.60" shape="8.00,-16.80 8.00,16.80"/>
        <lane id=":intersection2_9_1" index="1" speed="13.89" length="33.60" shape="4.80,-16.80 4.80,16.80"/>
    </edge>
    <edge id=":intersection2_11" function="internal">
        <lane id=":intersection2_11_0" index="0" speed="11.36" length="29.67" shape="1.60,-16.80 0.45,-8.75 -3.00,-3.00 -8.75,0.45 -16.80,1.60"/>
    </edge>
    <edge id=":intersection2_12" function="internal">
        <lane id=":intersection2_12_0" index="0" speed="6.51" length="9.03" shape="-16.80,-11.20 -14.35,-11.55 -12.60,-12.60 -11.55,-14.35 -11.20,-16.80"/>
    </edge>
    <edge id=":intersection2_13" function="internal">
        <lane id=":intersection2_13_0" index="0" speed="13.89" length="33.60" shape="-16.80,-8.00 16.80,-8.00"/>
        <lane id=":intersection2_13_1" index="1" speed="13.89" length="33.60" shape="-16.80,-4.80 16.80,-4.80"/>
    </edge>
    <edge id=":intersection2_15" function="internal">
        <lane id=":intersection2_15_0" index="0" speed="11.36" length="29.67" shape="-16.80,-1.60 -8.75,-0.45 -3.00,3.00 0.45,8.75 1.60,16.80"/>
    </edge>

    <edge id="-E0" from="J1" to="intersection2" priority="-1">
        <lane id="-E0_0" index="0" speed="13.89" length="416.40" shape="433.20,11.20 16.80,11.20"/>
        <lane id="-E0_1" index="1" speed="13.89" length="416.40" shape="433.20,8.00 16.80,8.00"/>
        <lane id="-E0_2" index="2" speed="13.89" length="416.40" shape="433.20,4.80 16.80,4.80"/>
        <lane id="-E0_3" index="3" speed="13.89" length="416.40" shape="433.20,1.60 16.80,1.60"/>
    </edge>
    <edge id="-E1" from="intersection1" to="intersection2" priority="-1">
        <lane id="-E1_0" index="0" speed="13.89" length="416.40" shape="-433.20,-11.20 -16.80,-11.20"/>
        <lane id="-E1_1" index="1" speed="13.89" length="416.40" shape="-433.20,-8.00 -16.80,-8.00"/>
        <lane id="-E1_2" index="2" speed="13.89" length="416.40" shape="-433.20,-4.80 -16.80,-4.80"/>
        <lane id="-E1_3" index="3" speed="13.89" length="416.40" shape="-433.20,-1.60 -16.80,-1.60"/>
    </edge>
    <edge id="-E2" from="J3" to="intersection2" priority="-1">
        <lane id="-E2_0" index="0" speed="13.89" length="283.20" shape="-11.20,300.00 -11.20,16.80"/>
        <lane id="-E2_1" index="1" speed="13.89" length="283.20" shape="-8.00,300.00 -8.00,16.80"/>
        <lane id="-E2_2" index="2" speed="13.89" length="283.20" shape="-4.80,300.00 -4.80,16.80"/>
        <lane id="-E2_3" index="3" speed="13.89" length="283.20" shape="-1.60,300.00 -1.60,16.80"/>
    </edge>
    <edge id="-E3" from="J4" to="intersection2" priority="-1">
        <lane id="-E3_0" index="0" speed="13.89" length="283.20" shape="11.20,-300.00 11.20,-16.80"/>
        <lane id="-E3_1" index="1" speed="13.89" length="283.20" shape="8.00,-300.00 8.00,-16.80"/>
        <lane id="-E3_2" index="2" speed="13.89" length="283.20" shape="4.80,-300.00 4.80,-16.80"/>
        <lane id="-E3_3" index="3" speed="13.89" length="283.20" shape="1.60,-300.00 1.60,-16.80"/>
    </edge>
    <edge id="-E4" from="J5" to="J1" priority="-1">
        <lane id="-E4_0" index="0" speed="13.89" length="433.20" shape="900.00,11.20 466.80,11.20"/>
        <lane id="-E4_1" index="1" speed="13.89" length="433.20" shape="900.00,8.00 466.80,8.00"/>
        <lane id="-E4_2" index="2" speed="13.89" length="433.20" shape="900.00,4.80 466.80,4.80"/>
        <lane id="-E4_3" index="3" speed="13.89" length="433.20" shape="900.00,1.60 466.80,1.60"/>
    </edge>
    <edge id="-E5" from="J6" to="J1" priority="-1">
        <lane id="-E5_0" index="0" speed="13.89" length="283.20" shape="438.80,300.00 438.80,16.80"/>
        <lane id="-E5_1" index="1" speed="13.89" length="283.20" shape="442.00,300.00 442.00,16.80"/>
        <lane id="-E5_2" index="2" speed="13.89" length="283.20" shape="445.20,300.00 445.20,16.80"/>
        <lane id="-E5_3" index="3" speed="13.89" length="283.20" shape="448.40,300.00 448.40,16.80"/>
    </edge>
    <edge id="-E6" from="J7" to="J1" priority="-1">
        <lane id="-E6_0" index="0" speed="13.89" length="283.20" shape="461.20,-300.00 461.20,-16.80"/>
        <lane id="-E6_1" index="1" speed="13.89" length="283.20" shape="458.00,-300.00 458.00,-16.80"/>
        <lane id="-E6_2" index="2" speed="13.89" length="283.20" shape="454.80,-300.00 454.80,-16.80"/>
        <lane id="-E6_3" index="3" speed="13.89" length="283.20" shape="451.60,-300.00 451.60,-16.80"/>
    </edge>
    <edge id="-E7" from="J8" to="intersection1" priority="-1">
        <lane id="-E7_0" index="0" speed="13.89" length="283.20" shape="-461.20,300.00 -461.20,16.80"/>
        <lane id="-E7_1" index="1" speed="13.89" length="283.20" shape="-458.00,300.00 -458.00,16.80"/>
        <lane id="-E7_2" index="2" speed="13.89" length="283.20" shape="-454.80,300.00 -454.80,16.80"/>
        <lane id="-E7_3" index="3" speed="13.89" length="283.20" shape="-451.60,300.00 -451.60,16.80"/>
    </edge>
    <edge id="-E8" from="J9" to="intersection1" priority="-1">
        <lane id="-E8_0" index="0" speed="13.89" length="283.20" shape="-438.80,-300.00 -438.80,-16.80"/>
        <lane id="-E8_1" index="1" speed="13.89" length="283.20" shape="-442.00,-300.00 -442.00,-16.80"/>
        <lane id="-E8_2" index="2" speed="13.89" length="283.20" shape="-445.20,-300.00 -445.20,-16.80"/>
        <lane id="-E8_3" index="3" speed="13.89" length="283.20" shape="-448.40,-300.00 -448.40,-16.80"/>
    </edge>
    <edge id="-E9" from="J10" to="intersection1" priority="-1">
        <lane id="-E9_0" index="0" speed="13.89" length="433.20" shape="-900.00,-11.20 -466.80,-11.20"/>
        <lane id="-E9_1" index="1" speed="13.89" length="433.20" shape="-900.00,-8.00 -466.80,-8.00"/>
        <lane id="-E9_2" index="2" speed="13.89" length="433.20" shape="-900.00,-4.80 -466.80,-4.80"/>
        <lane id="-E9_3" index="3" speed="13.89" length="433.20" shape="-900.00,-1.60 -466.80,-1.60"/>
    </edge>
    <edge id="E0" from="intersection2" to="J1" priority="-1">
        <lane id="E0_0" index="0" speed="13.89" length="416.40" shape="16.80,-11.20 433.20,-11.20"/>
        <lane id="E0_1" index="1" speed="13.89" length="416.40" shape="16.80,-8.00 433.20,-8.00"/>
        <lane id="E0_2" index="2" speed="13.89" length="416.40" shape="16.80,-4.80 433.20,-4.80"/>
        <lane id="E0_3" index="3" speed="13.89" length="416.40" shape="16.80,-1.60 433.20,-1.60"/>
    </edge>
    <edge id="E1" from="intersection2" to="intersection1" priority="-1">
        <lane id="E1_0" index="0" speed="13.89" length="416.40" shape="-16.80,11.20 -433.20,11.20"/>
        <lane id="E1_1" index="1" speed="13.89" length="416.40" shape="-16.80,8.00 -433.20,8.00"/>
        <lane id="E1_2" index="2" speed="13.89" length="416.40" shape="-16.80,4.80 -433.20,4.80"/>
        <lane id="E1_3" index="3" speed="13.89" length="416.40" shape="-16.80,1.60 -433.20,1.60"/>
    </edge>
    <edge id="E2" from="intersection2" to="J3" priority="-1">
        <lane id="E2_0" index="0" speed="13.89" length="283.20" shape="11.20,16.80 11.20,300.00"/>
        <lane id="E2_1" index="1" speed="13.89" length="283.20" shape="8.00,16.80 8.00,300.00"/>
        <lane id="E2_2" index="2" speed="13.89" length="283.20" shape="4.80,16.80 4.80,300.00"/>
        <lane id="E2_3" index="3" speed="13.89" length="283.20" shape="1.60,16.80 1.60,300.00"/>
    </edge>
    <edge id="E3" from="intersection2" to="J4" priority="-1">
        <lane id="E3_0" index="0" speed="13.89" length="283.20" shape="-11.20,-16.80 -11.20,-300.00"/>
        <lane id="E3_1" index="1" speed="13.89" length="283.20" shape="-8.00,-16.80 -8.00,-300.00"/>
        <lane id="E3_2" index="2" speed="13.89" length="283.20" shape="-4.80,-16.80 -4.80,-300.00"/>
        <lane id="E3_3" index="3" speed="13.89" length="283.20" shape="-1.60,-16.80 -1.60,-300.00"/>
    </edge>
    <edge id="E4" from="J1" to="J5" priority="-1">
        <lane id="E4_0" index="0" speed="13.89" length="433.20" shape="466.80,-11.20 900.00,-11.20"/>
        <lane id="E4_1" index="1" speed="13.89" length="433.20" shape="466.80,-8.00 900.00,-8.00"/>
        <lane id="E4_2" index="2" speed="13.89" length="433.20" shape="466.80,-4.80 900.00,-4.80"/>
        <lane id="E4_3" index="3" speed="13.89" length="433.20" shape="466.80,-1.60 900.00,-1.60"/>
    </edge>
    <edge id="E5" from="J1" to="J6" priority="-1">
        <lane id="E5_0" index="0" speed="13.89" length="283.20" shape="461.20,16.80 461.20,300.00"/>
        <lane id="E5_1" index="1" speed="13.89" length="283.20" shape="458.00,16.80 458.00,300.00"/>
        <lane id="E5_2" index="2" speed="13.89" length="283.20" shape="454.80,16.80 454.80,300.00"/>
        <lane id="E5_3" index="3" speed="13.89" length="283.20" shape="451.60,16.80 451.60,300.00"/>
    </edge>
    <edge id="E6" from="J1" to="J7" priority="-1">
        <lane id="E6_0" index="0" speed="13.89" length="283.20" shape="438.80,-16.80 438.80,-300.00"/>
        <lane id="E6_1" index="1" speed="13.89" length="283.20" shape="442.00,-16.80 442.00,-300.00"/>
        <lane id="E6_2" index="2" speed="13.89" length="283.20" shape="445.20,-16.80 445.20,-300.00"/>
        <lane id="E6_3" index="3" speed="13.89" length="283.20" shape="448.40,-16.80 448.40,-300.00"/>
    </edge>
    <edge id="E7" from="intersection1" to="J8" priority="-1">
        <lane id="E7_0" index="0" speed="13.89" length="283.20" shape="-438.80,16.80 -438.80,300.00"/>
        <lane id="E7_1" index="1" speed="13.89" length="283.20" shape="-442.00,16.80 -442.00,300.00"/>
        <lane id="E7_2" index="2" speed="13.89" length="283.20" shape="-445.20,16.80 -445.20,300.00"/>
        <lane id="E7_3" index="3" speed="13.89" length="283.20" shape="-448.40,16.80 -448.40,300.00"/>
    </edge>
    <edge id="E8" from="intersection1" to="J9" priority="-1">
        <lane id="E8_0" index="0" speed="13.89" length="283.20" shape="-461.20,-16.80 -461.20,-300.00"/>
        <lane id="E8_1" index="1" speed="13.89" length="283.20" shape="-458.00,-16.80 -458.00,-300.00"/>
        <lane id="E8_2" index="2" speed="13.89" length="283.20" shape="-454.80,-16.80 -454.80,-300.00"/>
        <lane id="E8_3" index="3" speed="13.89" length="283.20" shape="-451.60,-16.80 -451.60,-300.00"/>
    </edge>
    <edge id="E9" from="intersection1" to="J10" priority="-1">
        <lane id="E9_0" index="0" speed="13.89" length="433.20" shape="-466.80,11.20 -900.00,11.20"/>
        <lane id="E9_1" index="1" speed="13.89" length="433.20" shape="-466.80,8.00 -900.00,8.00"/>
        <lane id="E9_2" index="2" speed="13.89" length="433.20" shape="-466.80,4.80 -900.00,4.80"/>
        <lane id="E9_3" index="3" speed="13.89" length="433.20" shape="-466.80,1.60 -900.00,1.60"/>
    </edge>

    <tlLogic id="light1" type="static" programID="0" offset="0">
        <phase duration="25" state="GrrrGGGrGrrrGGGr"/>
        <phase duration="20" state="GrrrGrrGGrrrGrrG"/>
        <phase duration="25" state="GGGrGrrrGGGrGrrr"/>
        <phase duration="20" state="GrrGGrrrGrrGGrrr"/>
    </tlLogic>
    <tlLogic id="light2" type="static" programID="0" offset="0">
        <phase duration="25" state="GrrrrrGGGrGrrrGGGr"/>
        <phase duration="20" state="GrrrrrGrrGGrrrGrrG"/>
        <phase duration="25" state="GGGGGrGrrrGGGrGrrr"/>
        <phase duration="20" state="GrrrGGGrrrGrrGGrrr"/>
    </tlLogic>
    <tlLogic id="light3" type="static" programID="0" offset="0">
        <phase duration="25" state="GrrrGGGrGrrrGGGr"/>
        <phase duration="20" state="GrrrGrrGGrrrGrrG"/>
        <phase duration="25" state="GGGrGrrrGGGrGrrr"/>
        <phase duration="20" state="GrrGGrrrGrrGGrrr"/>
    </tlLogic>

    <junction id="J1" type="traffic_light" x="450.00" y="0.00" incLanes="-E5_0 -E5_1 -E5_2 -E5_3 -E4_0 -E4_1 -E4_2 -E4_3 -E6_0 -E6_1 -E6_2 -E6_3 E0_0 E0_1 E0_2 E0_3" intLanes=":J1_0_0 :J1_1_0 :J1_1_1 :J1_3_0 :J1_4_0 :J1_5_0 :J1_5_1 :J1_7_0 :J1_8_0 :J1_9_0 :J1_9_1 :J1_11_0 :J1_12_0 :J1_13_0 :J1_13_1 :J1_15_0" shape="437.20,16.80 462.80,16.80 463.24,14.58 463.80,13.80 464.58,13.24 465.58,12.91 466.80,12.80 466.80,-12.80 464.58,-13.24 463.80,-13.80 463.24,-14.58 462.91,-15.58 462.80,-16.80 437.20,-16.80 436.76,-14.58 436.20,-13.80 435.42,-13.24 434.42,-12.91 433.20,-12.80 433.20,12.80 435.42,13.24 436.20,13.80 436.76,14.58 437.09,15.58">
        <request index="0"  response="0000000000000000" foes="0000000000000000" cont="0"/>
        <request index="1"  response="0000000000000000" foes="1110100001100000" cont="0"/>
        <request index="2"  response="0000000000000000" foes="1110100001100000" cont="0"/>
        <request index="3"  response="0000011000000000" foes="1000011011100000" cont="0"/>
        <request index="4"  response="0000000000000000" foes="0000000000000000" cont="0"/>
        <request index="5"  response="0000011000001110" foes="1000011000001110" cont="0"/>
        <request index="6"  response="0000011000001110" foes="1000011000001110" cont="0"/>
        <request index="7"  response="0110111000001000" foes="0110111000001000" cont="0"/>
        <request index="8"  response="0000000000000000" foes="0000000000000000" cont="0"/>
        <request index="9"  response="0000000000000000" foes="0110000011101000" cont="0"/>
        <request index="10" response="0000000000000000" foes="0110000011101000" cont="0"/>
        <request index="11" response="0000000000000110" foes="1110000010000110" cont="0"/>
        <request index="12" response="0000000000000000" foes="0000000000000000" cont="0"/>
        <request index="13" response="0000111000000110" foes="0000111010000110" cont="0"/>
        <request index="14" response="0000111000000110" foes="0000111010000110" cont="0"/>
        <request index="15" response="0000100001101110" foes="0000100001101110" cont="0"/>
    </junction>
    <junction id="J10" type="dead_end" x="-900.00" y="0.00" incLanes="E9_0 E9_1 E9_2 E9_3" intLanes="" shape="-900.00,0.00 -900.00,12.80 -900.00,0.00"/>
    <junction id="J3" type="dead_end" x="0.00" y="300.00" incLanes="E2_0 E2_1 E2_2 E2_3" intLanes="" shape="0.00,300.00 12.80,300.00 0.00,300.00"/>
    <junction id="J4" type="dead_end" x="0.00" y="-300.00" incLanes="E3_0 E3_1 E3_2 E3_3" intLanes="" shape="0.00,-300.00 -12.80,-300.00 0.00,-300.00"/>
    <junction id="J5" type="dead_end" x="900.00" y="0.00" incLanes="E4_0 E4_1 E4_2 E4_3" intLanes="" shape="900.00,0.00 900.00,-12.80 900.00,0.00"/>
    <junction id="J6" type="dead_end" x="450.00" y="300.00" incLanes="E5_0 E5_1 E5_2 E5_3" intLanes="" shape="450.00,300.00 462.80,300.00 450.00,300.00"/>
    <junction id="J7" type="dead_end" x="450.00" y="-300.00" incLanes="E6_0 E6_1 E6_2 E6_3" intLanes="" shape="450.00,-300.00 437.20,-300.00 450.00,-300.00"/>
    <junction id="J8" type="dead_end" x="-450.00" y="300.00" incLanes="E7_0 E7_1 E7_2 E7_3" intLanes="" shape="-450.00,300.00 -437.20,300.00 -450.00,300.00"/>
    <junction id="J9" type="dead_end" x="-450.00" y="-300.00" incLanes="E8_0 E8_1 E8_2 E8_3" intLanes="" shape="-450.00,-300.00 -462.80,-300.00 -450.00,-300.00"/>
    <junction id="intersection1" type="traffic_light" x="-450.00" y="0.00" incLanes="-E7_0 -E7_1 -E7_2 -E7_3 E1_0 E1_1 E1_2 E1_3 -E8_0 -E8_1 -E8_2 -E8_3 -E9_0 -E9_1 -E9_2 -E9_3" intLanes=":intersection1_0_0 :intersection1_1_0 :intersection1_1_1 :intersection1_3_0 :intersection1_4_0 :intersection1_5_0 :intersection1_5_1 :intersection1_7_0 :intersection1_8_0 :intersection1_9_0 :intersection1_9_1 :intersection1_11_0 :intersection1_12_0 :intersection1_13_0 :intersection1_13_1 :intersection1_15_0" shape="-462.80,16.80 -437.20,16.80 -436.76,14.58 -436.20,13.80 -435.42,13.24 -434.42,12.91 -433.20,12.80 -433.20,-12.80 -435.42,-13.24 -436.20,-13.80 -436.76,-14.58 -437.09,-15.58 -437.20,-16.80 -462.80,-16.80 -463.24,-14.58 -463.80,-13.80 -464.58,-13.24 -465.58,-12.91 -466.80,-12.80 -466.80,12.80 -464.58,13.24 -463.80,13.80 -463.24,14.58 -462.91,15.58">
        <request index="0"  response="0000000000000000" foes="0000000000000000" cont="0"/>
        <request index="1"  response="0000000000000000" foes="1110100001100000" cont="0"/>
        <request index="2"  response="0000000000000000" foes="1110100001100000" cont="0"/>
        <request index="3"  response="0000011000000000" foes="1000011011100000" cont="0"/>
        <request index="4"  response="0000000000000000" foes="0000000000000000" cont="0"/>
        <request index="5"  response="0000011000001110" foes="1000011000001110" cont="0"/>
        <request index="6"  response="0000011000001110" foes="1000011000001110" cont="0"/>
        <request index="7"  response="0110111000001000" foes="0110111000001000" cont="0"/>
        <request index="8"  response="0000000000000000" foes="0000000000000000" cont="0"/>
        <request index="9"  response="0000000000000000" foes="0110000011101000" cont="0"/>
        <request index="10" response="0000000000000000" foes="0110000011101000" cont="0"/>
        <request index="11" response="0000000000000110" foes="1110000010000110" cont="0"/>
        <request index="12" response="0000000000000000" foes="0000000000000000" cont="0"/>
        <request index="13" response="0000111000000110" foes="0000111010000110" cont="0"/>
        <request index="14" response="0000111000000110" foes="0000111010000110" cont="0"/>
        <request index="15" response="0000100001101110" foes="0000100001101110" cont="0"/>
    </junction>
    <junction id="intersection2" type="traffic_light" x="0.00" y="0.00" incLanes="-E2_0 -E2_1 -E2_2 -E2_3 -E0_0 -E0_1 -E0_2 -E0_3 -E3_0 -E3_1 -E3_2 -E3_3 -E1_0 -E1_1 -E1_2 -E1_3" intLanes=":intersection2_0_0 :intersection2_1_0 :intersection2_1_1 :intersection2_3_0 :intersection2_4_0 :intersection2_5_0 :intersection2_5_1 :intersection2_7_0 :intersection2_8_0 :intersection2_9_0 :intersection2_9_1 :intersection2_11_0 :intersection2_12_0 :intersection2_13_0 :intersection2_13_1 :intersection2_15_0" shape="-12.80,16.80 12.80,16.80 13.24,14.58 13.80,13.80 14.58,13.24 15.58,12.91 16.80,12.80 16.80,-12.80 14.58,-13.24 13.80,-13.80 13.24,-14.58 12.91,-15.58 12.80,-16.80 -12.80,-16.80 -13.24,-14.58 -13.80,-13.80 -14.58,-13.24 -15.58,-12.91 -16.80,-12.80 -16.80,12.80 -14.58,13.24 -13.80,13.80 -13.24,14.58 -12.91,15.58">
        <request index="0"  response="0000000000000000" foes="0000000000000000" cont="0"/>
        <request index="1"  response="0000000000000000" foes="1110100001100000" cont="0"/>
        <request index="2"  response="0000000000000000" foes="1110100001100000" cont="0"/>
        <request index="3"  response="0000011000000000" foes="1000011011100000" cont="0"/>
        <request index="4"  response="0000000000000000" foes="0000000000000000" cont="0"/>
        <request index="5"  response="0000011000001110" foes="1000011000001110" cont="0"/>
        <request index="6"  response="0000011000001110" foes="1000011000001110" cont="0"/>
        <request index="7"  response="0110111000001000" foes="0110111000001000" cont="0"/>
        <request index="8"  response="0000000000000000" foes="0000000000000000" cont="0"/>
        <request index="9"  response="0000000000000000" foes="0110000011101000" cont="0"/>
        <request index="10" response="0000000000000000" foes="0110000011101000" cont="0"/>
        <request index="11" response="0000000000000110" foes="1110000010000110" cont="0"/>
        <request index="12" response="0000000000000000" foes="0000000000000000" cont="0"/>
        <request index="13" response="0000111000000110" foes="0000111010000110" cont="0"/>
        <request index="14" response="0000111000000110" foes="0000111010000110" cont="0"/>
        <request index="15" response="0000100001101110" foes="0000100001101110" cont="0"/>
    </junction>

    <connection from="-E0" to="E2" fromLane="0" toLane="0" via=":intersection2_4_0" tl="light2" linkIndex="6" dir="r" state="O"/>
    <connection from="-E0" to="E1" fromLane="1" toLane="1" via=":intersection2_5_0" tl="light2" linkIndex="7" dir="s" state="o"/>
    <connection from="-E0" to="E1" fromLane="2" toLane="2" via=":intersection2_5_1" tl="light2" linkIndex="8" dir="s" state="o"/>
    <connection from="-E0" to="E3" fromLane="3" toLane="3" via=":intersection2_7_0" tl="light2" linkIndex="9" dir="l" state="o"/>
    <connection from="-E1" to="E3" fromLane="0" toLane="0" via=":intersection2_12_0" tl="light2" linkIndex="14" dir="r" state="O"/>
    <connection from="-E1" to="E0" fromLane="1" toLane="1" via=":intersection2_13_0" tl="light2" linkIndex="15" dir="s" state="o"/>
    <connection from="-E1" to="E0" fromLane="2" toLane="2" via=":intersection2_13_1" tl="light2" linkIndex="16" dir="s" state="o"/>
    <connection from="-E1" to="E2" fromLane="3" toLane="3" via=":intersection2_15_0" tl="light2" linkIndex="17" dir="l" state="o"/>
    <connection from="-E2" to="E1" fromLane="0" toLane="0" via=":intersection2_0_0" tl="light2" linkIndex="0" dir="r" state="O"/>
    <connection from="-E2" to="E3" fromLane="1" toLane="1" via=":intersection2_1_0" tl="light2" linkIndex="2" dir="s" state="O"/>
    <connection from="-E2" to="E3" fromLane="2" toLane="2" via=":intersection2_1_1" tl="light2" linkIndex="3" dir="s" state="O"/>
    <connection from="-E2" to="E0" fromLane="3" toLane="3" via=":intersection2_3_0" tl="light2" linkIndex="5" dir="l" state="o"/>
    <connection from="-E3" to="E0" fromLane="0" toLane="0" via=":intersection2_8_0" tl="light2" linkIndex="10" dir="r" state="O"/>
    <connection from="-E3" to="E2" fromLane="1" toLane="1" via=":intersection2_9_0" tl="light2" linkIndex="11" dir="s" state="O"/>
    <connection from="-E3" to="E2" fromLane="2" toLane="2" via=":intersection2_9_1" tl="light2" linkIndex="12" dir="s" state="O"/>
    <connection from="-E3" to="E1" fromLane="3" toLane="3" via=":intersection2_11_0" tl="light2" linkIndex="13" dir="l" state="o"/>
    <connection from="-E4" to="E5" fromLane="0" toLane="0" via=":J1_4_0" tl="light3" linkIndex="4" dir="r" state="O"/>
    <connection from="-E4" to="-E0" fromLane="1" toLane="1" via=":J1_5_0" tl="light3" linkIndex="5" dir="s" state="o"/>
    <connection from="-E4" to="-E0" fromLane="2" toLane="2" via=":J1_5_1" tl="light3" linkIndex="6" dir="s" state="o"/>
    <connection from="-E4" to="E6" fromLane="3" toLane="3" via=":J1_7_0" tl="light3" linkIndex="7" dir="l" state="o"/>
    <connection from="-E5" to="-E0" fromLane="0" toLane="0" via=":J1_0_0" tl="light3" linkIndex="0" dir="r" state="O"/>
    <connection from="-E5" to="E6" fromLane="1" toLane="1" via=":J1_1_0" tl="light3" linkIndex="1" dir="s" state="O"/>
    <connection from="-E5" to="E6" fromLane="2" toLane="2" via=":J1_1_1" tl="light3" linkIndex="2" dir="s" state="O"/>
    <connection from="-E5" to="E4" fromLane="3" toLane="3" via=":J1_3_0" tl="light3" linkIndex="3" dir="l" state="o"/>
    <connection from="-E6" to="E4" fromLane="0" toLane="0" via=":J1_8_0" tl="light3" linkIndex="8" dir="r" state="O"/>
    <connection from="-E6" to="E5" fromLane="1" toLane="1" via=":J1_9_0" tl="light3" linkIndex="9" dir="s" state="O"/>
    <connection from="-E6" to="E5" fromLane="2" toLane="2" via=":J1_9_1" tl="light3" linkIndex="10" dir="s" state="O"/>
    <connection from="-E6" to="-E0" fromLane="3" toLane="3" via=":J1_11_0" tl="light3" linkIndex="11" dir="l" state="o"/>
    <connection from="-E7" to="E9" fromLane="0" toLane="0" via=":intersection1_0_0" tl="light1" linkIndex="0" dir="r" state="O"/>
    <connection from="-E7" to="E8" fromLane="1" toLane="1" via=":intersection1_1_0" tl="light1" linkIndex="1" dir="s" state="O"/>
    <connection from="-E7" to="E8" fromLane="2" toLane="2" via=":intersection1_1_1" tl="light1" linkIndex="2" dir="s" state="O"/>
    <connection from="-E7" to="-E1" fromLane="3" toLane="3" via=":intersection1_3_0" tl="light1" linkIndex="3" dir="l" state="o"/>
    <connection from="-E8" to="-E1" fromLane="0" toLane="0" via=":intersection1_8_0" tl="light1" linkIndex="8" dir="r" state="O"/>
    <connection from="-E8" to="E7" fromLane="1" toLane="1" via=":intersection1_9_0" tl="light1" linkIndex="9" dir="s" state="O"/>
    <connection from="-E8" to="E7" fromLane="2" toLane="2" via=":intersection1_9_1" tl="light1" linkIndex="10" dir="s" state="O"/>
    <connection from="-E8" to="E9" fromLane="3" toLane="3" via=":intersection1_11_0" tl="light1" linkIndex="11" dir="l" state="o"/>
    <connection from="-E9" to="E8" fromLane="0" toLane="0" via=":intersection1_12_0" tl="light1" linkIndex="12" dir="r" state="O"/>
    <connection from="-E9" to="-E1" fromLane="1" toLane="1" via=":intersection1_13_0" tl="light1" linkIndex="13" dir="s" state="o"/>
    <connection from="-E9" to="-E1" fromLane="2" toLane="2" via=":intersection1_13_1" tl="light1" linkIndex="14" dir="s" state="o"/>
    <connection from="-E9" to="E7" fromLane="3" toLane="3" via=":intersection1_15_0" tl="light1" linkIndex="15" dir="l" state="o"/>
    <connection from="E0" to="E6" fromLane="0" toLane="0" via=":J1_12_0" tl="light3" linkIndex="12" dir="r" state="O"/>
    <connection from="E0" to="E4" fromLane="1" toLane="1" via=":J1_13_0" tl="light3" linkIndex="13" dir="s" state="o"/>
    <connection from="E0" to="E4" fromLane="2" toLane="2" via=":J1_13_1" tl="light3" linkIndex="14" dir="s" state="o"/>
    <connection from="E0" to="E5" fromLane="3" toLane="3" via=":J1_15_0" tl="light3" linkIndex="15" dir="l" state="o"/>
    <connection from="E1" to="E7" fromLane="0" toLane="0" via=":intersection1_4_0" tl="light1" linkIndex="4" dir="r" state="O"/>
    <connection from="E1" to="E9" fromLane="1" toLane="1" via=":intersection1_5_0" tl="light1" linkIndex="5" dir="s" state="o"/>
    <connection from="E1" to="E9" fromLane="2" toLane="2" via=":intersection1_5_1" tl="light1" linkIndex="6" dir="s" state="o"/>
    <connection from="E1" to="E8" fromLane="3" toLane="3" via=":intersection1_7_0" tl="light1" linkIndex="7" dir="l" state="o"/>

    <connection from=":J1_0" to="-E0" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":J1_1" to="E6" fromLane="0" toLane="1" dir="s" state="M"/>
    <connection from=":J1_1" to="E6" fromLane="1" toLane="2" dir="s" state="M"/>
    <connection from=":J1_3" to="E4" fromLane="0" toLane="3" dir="l" state="M"/>
    <connection from=":J1_4" to="E5" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":J1_5" to="-E0" fromLane="0" toLane="1" dir="s" state="M"/>
    <connection from=":J1_5" to="-E0" fromLane="1" toLane="2" dir="s" state="M"/>
    <connection from=":J1_7" to="E6" fromLane="0" toLane="3" dir="l" state="M"/>
    <connection from=":J1_8" to="E4" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":J1_9" to="E5" fromLane="0" toLane="1" dir="s" state="M"/>
    <connection from=":J1_9" to="E5" fromLane="1" toLane="2" dir="s" state="M"/>
    <connection from=":J1_11" to="-E0" fromLane="0" toLane="3" dir="l" state="M"/>
    <connection from=":J1_12" to="E6" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":J1_13" to="E4" fromLane="0" toLane="1" dir="s" state="M"/>
    <connection from=":J1_13" to="E4" fromLane="1" toLane="2" dir="s" state="M"/>
    <connection from=":J1_15" to="E5" fromLane="0" toLane="3" dir="l" state="M"/>
    <connection from=":intersection1_0" to="E9" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":intersection1_1" to="E8" fromLane="0" toLane="1" dir="s" state="M"/>
    <connection from=":intersection1_1" to="E8" fromLane="1" toLane="2" dir="s" state="M"/>
    <connection from=":intersection1_3" to="-E1" fromLane="0" toLane="3" dir="l" state="M"/>
    <connection from=":intersection1_4" to="E7" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":intersection1_5" to="E9" fromLane="0" toLane="1" dir="s" state="M"/>
    <connection from=":intersection1_5" to="E9" fromLane="1" toLane="2" dir="s" state="M"/>
    <connection from=":intersection1_7" to="E8" fromLane="0" toLane="3" dir="l" state="M"/>
    <connection from=":intersection1_8" to="-E1" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":intersection1_9" to="E7" fromLane="0" toLane="1" dir="s" state="M"/>
    <connection from=":intersection1_9" to="E7" fromLane="1" toLane="2" dir="s" state="M"/>
    <connection from=":intersection1_11" to="E9" fromLane="0" toLane="3" dir="l" state="M"/>
    <connection from=":intersection1_12" to="E8" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":intersection1_13" to="-E1" fromLane="0" toLane="1" dir="s" state="M"/>
    <connection from=":intersection1_13" to="-E1" fromLane="1" toLane="2" dir="s" state="M"/>
    <connection from=":intersection1_15" to="E7" fromLane="0" toLane="3" dir="l" state="M"/>
    <connection from=":intersection2_0" to="E1" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":intersection2_1" to="E3" fromLane="0" toLane="1" dir="s" state="M"/>
    <connection from=":intersection2_1" to="E3" fromLane="1" toLane="2" dir="s" state="M"/>
    <connection from=":intersection2_3" to="E0" fromLane="0" toLane="3" dir="l" state="M"/>
    <connection from=":intersection2_4" to="E2" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":intersection2_5" to="E1" fromLane="0" toLane="1" dir="s" state="M"/>
    <connection from=":intersection2_5" to="E1" fromLane="1" toLane="2" dir="s" state="M"/>
    <connection from=":intersection2_7" to="E3" fromLane="0" toLane="3" dir="l" state="M"/>
    <connection from=":intersection2_8" to="E0" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":intersection2_9" to="E2" fromLane="0" toLane="1" dir="s" state="M"/>
    <connection from=":intersection2_9" to="E2" fromLane="1" toLane="2" dir="s" state="M"/>
    <connection from=":intersection2_11" to="E1" fromLane="0" toLane="3" dir="l" state="M"/>
    <connection from=":intersection2_12" to="E3" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":intersection2_13" to="E0" fromLane="0" toLane="1" dir="s" state="M"/>
    <connection from=":intersection2_13" to="E0" fromLane="1" toLane="2" dir="s" state="M"/>
    <connection from=":intersection2_15" to="E2" fromLane="0" toLane="3" dir="l" state="M"/>

</net>
