import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 读取三个CSV文件
intersection1_data_no = pd.read_csv('初始信号下一号交叉口的延误.csv')
intersection2_data_no = pd.read_csv('初始信号下二号交叉口的延误.csv')
intersection3_data_no = pd.read_csv('初始信号下三号交叉口的延误.csv')

intersection1_data_vehicle = pd.read_csv('基于车流下一号交叉口的延误.csv')
intersection2_data_vehicle = pd.read_csv('基于车流下二号交叉口的延误.csv')
intersection3_data_vehicle = pd.read_csv('基于车流下三号交叉口的延误.csv')

intersection1_data_clxt = pd.read_csv('车路协同下一号交叉口的延误.csv')
intersection2_data_clxt = pd.read_csv('车路协同下二号交叉口的延误.csv')
intersection3_data_clxt = pd.read_csv('车路协同下三号交叉口的延误.csv')
# 提取第40到20个周期的数据
selected_data_intersection1_no = intersection1_data_no.iloc[:40, :42]
selected_data_intersection2_no = intersection2_data_no.iloc[:40, :42]
selected_data_intersection3_no = intersection3_data_no.iloc[:40, :42]

selected_data_intersection1_vehicle = intersection1_data_vehicle.iloc[:40, :42]
selected_data_intersection2_vehicle = intersection2_data_vehicle.iloc[:40, :42]
selected_data_intersection3_vehicle = intersection3_data_vehicle.iloc[:40, :42]

selected_data_intersection1_clxt = intersection1_data_clxt.iloc[:40, :42]
selected_data_intersection2_clxt = intersection2_data_clxt.iloc[:40, :42]
selected_data_intersection3_clxt = intersection3_data_clxt.iloc[:40, :42]

# 合并三个交叉口同一周期的delay_bus数据
delay_bus_total_no = (selected_data_intersection1_no['delay_person'].values.astype(float) +
                  selected_data_intersection2_no['delay_person'].values.astype(float) +
                  selected_data_intersection3_no['delay_person'].values.astype(float))

delay_bus_total_vehicle = (selected_data_intersection1_vehicle['delay_person'].values.astype(float) +
                  selected_data_intersection2_vehicle['delay_person'].values.astype(float) +
                  selected_data_intersection3_vehicle['delay_person'].values.astype(float))

delay_bus_total_clxt = (selected_data_intersection1_clxt['delay_person'].values.astype(float) +
                  selected_data_intersection2_clxt['delay_person'].values.astype(float) +
                  selected_data_intersection3_clxt['delay_person'].values.astype(float))

# 构建雷达图
num_vars = 40  # 假设有40个周期
angles = np.linspace(0, 2 * np.pi, num_vars, endpoint=False).tolist()

fig, ax = plt.subplots(figsize=(6, 6), subplot_kw=dict(polar=True))
ax.set_theta_offset(np.pi / 2)
ax.set_theta_direction(-1)

plt.xticks(angles, range(40))  # 假设周期是从0到39

# 绘制同一周期内三个交叉口delay_bus的总和的雷达图
ax.plot(angles, delay_bus_total_no, linewidth=1, linestyle='solid', label='Total Delay Bus-Initial signal timing', color='gray')
ax.plot(angles, delay_bus_total_vehicle, linewidth=1, linestyle='solid', label='Total Delay Bus-based on vehicle', color='blue')
ax.plot(angles, delay_bus_total_clxt, linewidth=1, linestyle='solid', label='Total Delay Bus-clxt signal timing', color='red')

ax.fill(angles, delay_bus_total_no, alpha=0.1, color='gray')
ax.fill(angles, delay_bus_total_vehicle, alpha=0.1, color='blue')
ax.fill(angles, delay_bus_total_clxt, alpha=0.1, color='red')

# 连接首尾以形成封闭图形
ax.plot([angles[0], angles[-1]], [delay_bus_total_no[0], delay_bus_total_no[-1]], linewidth=1, linestyle='solid', color='gray')
ax.plot([angles[0], angles[-1]], [delay_bus_total_vehicle[0], delay_bus_total_vehicle[-1]], linewidth=1, linestyle='solid', color='blue')
ax.plot([angles[0], angles[-1]], [delay_bus_total_clxt[0], delay_bus_total_clxt[-1]], linewidth=1, linestyle='solid', color='red')

plt.legend(loc='upper right')
plt.show()
