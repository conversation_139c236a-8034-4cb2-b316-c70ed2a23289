import traci
import pandas as pd
import sumolib
import random
import warnings
warnings.filterwarnings("ignore")
import csv
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.font_manager import FontProperties
font = FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf", size=10)

import xml.etree.ElementTree as ET

class Bus:
    def __init__(self, bus_id, position, speed):
        self.bus_id = bus_id
        self.position = position
        self.speed = speed

def update_speed(buses, desired_distance, max_acceleration, max_deceleration, time_step):
    # Process buses in groups of three
    for i in range(0, len(buses), 3):
        group = buses[i:i + 3]
        if len(group) < 2:
            continue

        for j in range(1, len(group)):
            leader = group[j - 1]
            follower = group[j]

            distance = leader.position - follower.position
            speed_difference = leader.speed - follower.speed

            # Calculate the required acceleration/deceleration
            if distance > desired_distance:
                acceleration = min(max_acceleration, (distance - desired_distance) / time_step)
            else:
                acceleration = max(-max_deceleration, (distance - desired_distance) / time_step)

            # Update the speed of the follower
            follower.speed += acceleration * time_step


def update_position(buses, time_step):
    for bus in buses:
        bus.position += bus.speed * time_step


def parse_bus_flow(file_path):
    tree = ET.parse(file_path)
    root = tree.getroot()
    buses = []
    bus_id_counter = 1
    for flow in root.findall('flow'):
        number_of_buses = int(flow.get('number'))
        for i in range(number_of_buses):
            bus_id = f"bus_{bus_id_counter}"
            position = random.uniform(-number_of_buses * 10, 0)
            speed = random.uniform(15, 25)
            buses.append(Bus(bus_id, position, speed))
            bus_id_counter += 1
    return buses


def main():

    time_step = 0.1
    total_time = 10
    desired_distance = 2
    max_acceleration = 3
    max_deceleration = 2

    bus_flow_file_path = '三个交叉口车流(lx).xml'
    buses = parse_bus_flow(bus_flow_file_path)

    # Run the simulation
    for t in range(int(total_time / time_step)):
        update_speed(buses, desired_distance, max_acceleration, max_deceleration, time_step)
        update_position(buses, time_step)

        # Print positions and speeds for monitoring
        for bus in buses:
            print(
                f"Time: {t * time_step:.1f}s, Bus ID: {bus.bus_id}, Position: {bus.position:.1f}m, Speed: {bus.speed:.1f}m/s")


#相位剩余时间
def phase_remain_time(tls_id):
    global remain_time
    simulation_time=traci.simulation.getTime()
    next_switch_time=traci.trafficlight.getNextSwitch(tls_id)
    phase_id=traci.trafficlight.getPhase(tls_id)
    remain_time=next_switch_time-simulation_time
    return remain_time

#获取信号灯各相位持续时间
def get_duration_time(tls_id):
    tl_def = traci.trafficlight.getAllProgramLogics(tls_id)
    program = traci.trafficlight.getProgram(tls_id)
    obj_logic = [_i for _i in tl_def if _i.getSubID() == program][0]
    phase_duration = []
    for phase in obj_logic.phases:
        a=phase.duration
        phase_duration.append(a)
    return phase_duration

#获取下周期绿灯时间
def next_phase_green_time(tls,a,b):
    if b < a:
        result=sum(tls[b:a])
    else:
        result=sum(tls[b:])+sum(tls[:a])
    return result

#获取速度控制公交前方的社会车排队长度
def car_num_before_bus(bus_id):
    lane_id = traci.vehicle.getLaneID(bus_id)
    car_id_before_bus = traci.lane.getLastStepVehicleIDs(lane_id)
    for i in car_id_before_bus:
        v_type = traci.vehicle.getTypeID(i)
        if v_type =="car":
            car_before_bus.append(i)
    car_before_bus_length = len(car_before_bus)*(car_length + car_gaps)
    return car_before_bus_length


#轨迹推算1
def trajectory_estimation1(lane1_id,bus_id,next_phase_time):

    #获取车辆在路段上的剩余位置
    remain_distance = traci.lane.getLength(lane1_id) - traci.vehicle.getLanePosition(bus_id)
    if remain_time != 0:
        predict_distance = remain_time*traci.vehicle.getSpeed(bus_id)
        predict_distance_max = remain_time * bus_max_speed
        if predict_distance >= remain_distance or predict_distance_max >= remain_distance:
            traci.vehicle.setSpeed(bus_id,bus_max_speed)
        else:
            #计算减速点
            dec_point = (remain_time + signal_cycle) * bus_min_speed
            Dec_point[bus_id] = dec_point

    else:
        predict_distance = next_phase_time * traci.vehicle.getSpeed(bus_id)
        if predict_distance <= remain_distance:
            traci.vehicle.setSpeed(bus_id,bus_min_speed)
        else:
            #计算减速点
            dec_point = next_phase_time*bus_min_speed
            Dec_point[bus_id] = dec_point

    return Dec_point

#轨迹推算1
def trajectory_estimation2(lane1_id,bus_id,next_phase_time):
    #获取车辆在路段上的剩余位置
    remain_distance = traci.lane.getLength(lane1_id) - traci.vehicle.getLanePosition(bus_id)
    if remain_time != 0:
        predict_distance = remain_time*traci.vehicle.getSpeed(bus_id)
        predict_distance_max = remain_time * bus_max_speed
        if predict_distance >= remain_distance or predict_distance_max >= remain_distance:
            pass
        else:
            #计算减速点
            dec_point = (remain_time + next_phase_time) * bus_min_speed
            Dec_point[bus_id] = dec_point

    else:
        predict_distance = next_phase_time * traci.vehicle.getSpeed(bus_id)
        if predict_distance <= remain_distance:
            pass
        else:
            #计算减速点
            dec_point = next_phase_time*bus_min_speed
            Dec_point[bus_id] = dec_point

    return Dec_point

#1-公交速度控制
def speed_control(dec_point,bus_id,lane1_id,remain_time):
    #路段长度
    road_length1=traci.lane.getLength(lane1_id)
    #距离路口的距离
    remain_distance = road_length1- traci.vehicle.getLanePosition(bus_id)
    if dec_point is None:
        traci.vehicle.setSpeed(bus_id,bus_min_speed)
    elif dec_point <= road_length1:
        if road_length1 - traci.vehicle.getLanePosition(bus_id) <= dec_point:
            traci.vehicle.setSpeed(bus_id,bus_min_speed)
    else:
        dec_point2=dec_point-traci.lane.getLength(lane1_id)
        if road_length1-traci.vehicle.getLanePosition(bus_id) > dec_point2:
            traci.vehicle.setSpeed(bus_id,bus_max_speed)

#获取下一个路段的ID
def get_next_route_id(vehicle_id):
    global next_route_id
    #获取当前路段的id
    route = traci.vehicle.getRoute(vehicle_id)
    #获取当前路段的中下标
    index = traci.vehicle.getRouteIndex(vehicle_id)
    #如果下标小于路段长度减一，说明还有下一个路段
    if index < len(route) -1 :
        #下一个路段的id
        next_route_id = route[index +1]
        return next_route_id
    return None

# 基于延误的乘客数--车路协同控制(clxt)
signal_plan_clxt_1crossroads = pd.read_csv('C:/Users/<USER>/Desktop/毕业/TSP/signal_time/优化后的信号配时方案_交叉口1.csv')
clxt_trigger_time_list_1crossroads = signal_plan_clxt_1crossroads['Cycle'].unique().tolist()

signal_plan_clxt_2crossroads = pd.read_csv('C:/Users/<USER>/Desktop/毕业/TSP/signal_time/优化后的信号配时方案_交叉口2.csv')
clxt_trigger_time_list_2crossroads = signal_plan_clxt_2crossroads['Cycle'].unique().tolist()

signal_plan_clxt_3crossroads = pd.read_csv('C:/Users/<USER>/Desktop/毕业/TSP/signal_time/优化后的信号配时方案_交叉口3.csv')
clxt_trigger_time_list_3crossroads = signal_plan_clxt_3crossroads['Cycle'].unique().tolist()

# 连接sumo
sumo_binary = "C:/Program Files (x86)/Eclipse/Sumo/bin/sumo-gui.exe"
sumo_cfg_file = "NormalIntersection.sumocfg"
traci.start([sumo_binary, "-c", sumo_cfg_file])

# 全局phase(三号交叉口)定义
phase_dict_1crossroads = {'1': 'GrrrGGGrGrrrGGGr',
                          '2': 'GrrrGrrGGrrrGrrG',
                          '3': 'GGGrGrrrGGGrGrrr',
                          '4': 'GrrGGrrrGrrGGrrr'}

phase_dict_2crossroads = {'1': 'GrrrrGGGGrGrrrrGGGGr',
                          '2': 'GrrrrGrrrGGrrrrGrrrG',
                          '3': 'GGGGrGrrrrGGGGrGrrrr',
                          '4': 'GrrrGGrrrrGrrrGGrrrr'}

phase_dict_3crossroads = {'1': 'GrrrGGGrGrrrGGGr',
                          '2': 'GrrrGrrGGrrrGrrG',
                          '3': 'GGGrGrrrGGGrGrrr',
                          '4': 'GrrGGrrrGrrGGrrr'}
#信号灯名称
junction_id_1crossroads = "light1"
junction_id_2crossroads = "light2"
junction_id_3crossroads = "light3"

#信号配时接收
#一号交叉口
clxt_phase_duration_dict_1crossroads = signal_plan_clxt_1crossroads.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()
clxt_logic_dict_1crossroads = {}
for phase_time, duration_dict in clxt_phase_duration_dict_1crossroads.items():
    phase_list = []
    for phase, state in phase_dict_1crossroads.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))
    clxt_logic_dict_1crossroads.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#二号交叉口
clxt_phase_duration_dict_2crossroads = signal_plan_clxt_2crossroads.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()
clxt_logic_dict_2crossroads = {}
for phase_time, duration_dict in clxt_phase_duration_dict_2crossroads.items():
    phase_list = []
    for phase, state in phase_dict_2crossroads.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))
    clxt_logic_dict_2crossroads.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#三号交叉口
clxt_phase_duration_dict_3crossroads = signal_plan_clxt_3crossroads.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()
clxt_logic_dict_3crossroads = {}
for phase_time, duration_dict in clxt_phase_duration_dict_3crossroads.items():
    phase_list = []
    for phase, state in phase_dict_3crossroads.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))
    clxt_logic_dict_3crossroads.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#参数定义
car_length = 5
car_gaps = 3
bus_max_speed=14
bus_min_speed=4
signal_cycle=90
person_num_bus=random.randint(25,35)
person_num_car=1.5
vehicle_tracking_clxt_1crossroads=[]
vehicle_tracking_clxt_2crossroads=[]
vehicle_tracking_clxt_3crossroads=[]
track_cycle_clxt = []

current_time = 0
cycle_start_time=current_time

car_count_clxt_1crossroads = []
car_count_clxt_2crossroads = []
car_count_clxt_3crossroads = []
bus_count_clxt_1crossroads = []
bus_count_clxt_2crossroads = []
bus_count_clxt_3crossroads = []

cycles_clxt_1crossroads=[]
cycles_clxt_2crossroads=[]
cycles_clxt_3crossroads=[]

car_in_cycle_clxt_1crossroads=[]
car_in_cycle_clxt_2crossroads=[]
car_in_cycle_clxt_3crossroads=[]

bus_in_cycle_clxt_1crossroads=[]
bus_in_cycle_clxt_2crossroads=[]
bus_in_cycle_clxt_3crossroads=[]

person_in_cycle_clxt_1crossroads=[]
person_in_cycle_clxt_2crossroads=[]
person_in_cycle_clxt_3crossroads=[]

next_phase_time_1crossroads=0
next_phase_time_2crossroads=0
next_phase_time_3crossroads=0
# 被控公交列表
controled_bus_1crossroads=[]
controled_bus_2crossroads=[]
controled_bus_3crossroads=[]
#公交前方的社会车
car_before_bus = []
#公交减速点字典
Dec_point={}

#计算乘客延误时间
total_person_delay_clxt_1crossroads=0
total_person_delay_clxt_2crossroads=0
total_person_delay_clxt_3crossroads=0

bus_delay_clxt_1crossroads = 0
bus_delay_clxt_2crossroads = 0
bus_delay_clxt_3crossroads = 0

cycle_delay_clxt_1crossroads = 0
cycle_delay_clxt_2crossroads = 0
cycle_delay_clxt_3crossroads = 0

last_cycle_clxt_1crossroads = 0
last_cycle_clxt_2crossroads = 0
last_cycle_clxt_3crossroads = 0

simulation_delay_time_clxt_1crossroads = []
simulation_delay_time_clxt_2crossroads = []
simulation_delay_time_clxt_3crossroads = []

average_delay_time_clxt_1crossroads = []
average_delay_time_clxt_2crossroads = []
average_delay_time_clxt_3crossroads = []

bus_delay_time_clxt_1crossroads = []
bus_delay_time_clxt_2crossroads = []
bus_delay_time_clxt_3crossroads = []

#相位上的车道ID
phase_to_lane_1crossroads={"1":["phase1_1_2","phase1_1_1","phase1_3_1","phase1_3_2"],
                           "2":["phase1_1_3","phase1_3_3"],
                           "3":["phase1_2_1","phase1_2_2","phase1_4_2","phase1_4_1"],
                           "4":["phase1_4_3","phase1_2_3"]}

phase_to_lane_2crossroads={"1":["phase2_1_2","phase2_1_1","phase2_3_1","phase2_3_2"],
                           "2":["phase2_1_3","phase2_3_3"],
                           "3":["phase2_2_1","phase2_2_2","phase2_4_2","phase2_4_1"],
                           "4":["phase2_4_3","phase2_2_3"]}

phase_to_lane_3crossroads={"1":["phase3_1_2","phase3_1_1","phase3_3_1","phase3_3_2"],
                           "2":["phase3_1_3","phase3_3_3"],
                           "3":["phase3_2_1","phase3_2_2","phase3_4_2","phase3_4_1"],
                           "4":["phase3_4_3","phase3_2_3"]}


vehicle_data = {}
# while traci.simulation.getMinExpectedNumber() > 0:
while traci.simulation.getTime()<500:
    cycle_counter = current_time//signal_cycle + 1
    # main()

    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in clxt_logic_dict_1crossroads.items():
            traci.trafficlight.setProgramLogic('light1', logic)
        for phase_time, logic in clxt_logic_dict_2crossroads.items():
            traci.trafficlight.setProgramLogic('light2', logic)
        for phase_time, logic in clxt_logic_dict_3crossroads.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案（每周期切换一次）
    if cycle_counter in clxt_trigger_time_list_1crossroads:
        if cycle_counter not in track_cycle_clxt:
            traci.trafficlight.setProgram('light1', str(cycle_counter))
            traci.trafficlight.setProgram('light2', str(cycle_counter))
            traci.trafficlight.setProgram('light3', str(cycle_counter))
            track_cycle_clxt.append(cycle_counter)

    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #获取三个交叉口优先相位剩余绿灯时间（0相位）
    phase_duration_1crossroads = get_duration_time(junction_id_1crossroads)
    phase_duration_2crossroads = get_duration_time(junction_id_2crossroads)
    phase_duration_3crossroads = get_duration_time(junction_id_3crossroads)
    # print(f"周期{cycle_counter}信号配时方案：{phase_duration}")
    # 获取三个交叉口优先相位剩余绿灯时间（0相位）
    #一号交叉口优先相位剩余绿灯时间
    if traci.trafficlight.getPhase(junction_id_1crossroads) == 0:
        remain_time_1crossroads = phase_remain_time(junction_id_1crossroads)
        print(f"交叉口1优先相位剩余绿灯时间:{remain_time_1crossroads}")

    else:
        phase = traci.trafficlight.getPhase(junction_id_1crossroads)
        remain_time_no_1crossroads = phase_remain_time(junction_id_1crossroads)
        remain_time_1crossroads = 0
        next_phase_time_1crossroads = next_phase_green_time(phase_duration_1crossroads,0,phase) - phase_duration_1crossroads[phase] + remain_time_no_1crossroads
        print(f"下一相位切换时间:{next_phase_time_1crossroads}")

    # 二号交叉口优先相位剩余绿灯时间
    if traci.trafficlight.getPhase(junction_id_2crossroads) == 0:
        remain_time_2crossroads = phase_remain_time(junction_id_2crossroads)
        print(f"交叉口2优先相位剩余绿灯时间:{remain_time_2crossroads}")

    else:
        phase = traci.trafficlight.getPhase(junction_id_2crossroads)
        remain_time_no_2crossroads = phase_remain_time(junction_id_2crossroads)
        remain_time_2crossroads = 0
        next_phase_time_2crossroads = next_phase_green_time(phase_duration_2crossroads,0,phase) - phase_duration_2crossroads[phase] + remain_time_no_2crossroads
        print(f"下一相位切换时间:{next_phase_time_2crossroads}")

    # 三号交叉口优先相位剩余绿灯时间
    if traci.trafficlight.getPhase(junction_id_3crossroads) == 0:
        remain_time_3crossroads = phase_remain_time(junction_id_3crossroads)
        print(f"交叉口3优先相位剩余绿灯时间:{remain_time_3crossroads}")

    else:
        phase = traci.trafficlight.getPhase(junction_id_3crossroads)
        remain_time_no_3crossroads = phase_remain_time(junction_id_3crossroads)
        remain_time_3crossroads = 0
        next_phase_time_3crossroads = next_phase_green_time(phase_duration_3crossroads,0,phase) - phase_duration_3crossroads[phase] + remain_time_no_3crossroads
        print(f"下一相位切换时间:{next_phase_time_3crossroads}")

    #获取优先相位方向路段上的所有车辆id （0相位）
    vehicle_ids_1crossroads = traci.edge.getLastStepVehicleIDs("phase1_1") + traci.edge.getLastStepVehicleIDs("phase1_3")
    vehicle_ids_2crossroads = traci.edge.getLastStepVehicleIDs("phase2_1") + traci.edge.getLastStepVehicleIDs("phase2_3")
    vehicle_ids_3crossroads = traci.edge.getLastStepVehicleIDs("phase3_1") + traci.edge.getLastStepVehicleIDs("phase3_3")

    #-------------交叉口一优先相位公交速度控制--------------
    #判断是否是优先相位上的公交车，并添加到控制列表中
    for vehicle_id in vehicle_ids_1crossroads:
        vehicle_type = traci.vehicle.getTypeID(vehicle_id)
        next_route_id = get_next_route_id(vehicle_id)
        if vehicle_type =="bus" and (next_route_id=="519426479#4" or next_route_id=="-824115250#4"):
            if vehicle_id not in controled_bus_1crossroads:
                controled_bus_1crossroads.append(vehicle_id)

        #一号交叉口南进口道直行
        vehicle_list_1crossroads = traci.lane.getLastStepVehicleIDs('phase1_1_2') + traci.lane.getLastStepVehicleIDs('phase1_1_1')
        for i in vehicle_list_1crossroads:
            if remain_time_1crossroads != 0 and i in controled_bus_1crossroads:
                remain_diatance = traci.lane.getLength("phase1_1_2") - traci.vehicle.getLanePosition(i)- car_num_before_bus(i)
                diatance_max = remain_time_1crossroads * bus_max_speed
                diatance_min = remain_time_1crossroads * bus_min_speed

                if diatance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i,bus_max_speed)
                elif diatance_min <= remain_diatance <= diatance_max:
                    traci.vehicle.setSpeed(i,bus_max_speed)
                else:
                    traci.vehicle.setSpeed(i,bus_min_speed)
            elif remain_time_1crossroads == 0 and i in controled_bus_1crossroads:
                remain_diatance = traci.lane.getLength("phase1_1_2") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time_1crossroads
                distance_min = bus_min_speed * next_phase_time_1crossroads
                if distance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i,bus_min_speed)
                elif distance_min <= remain_diatance <= distance_max:
                    traci.vehicle.setSpeed(i,bus_min_speed)
                elif distance_max <= remain_diatance:
                    traci.vehicle.setSpeed(i,bus_max_speed)

        #一号交叉口北进口道直行
        vehicle_list_1crossroads = traci.lane.getLastStepVehicleIDs('phase1_3_1') + traci.lane.getLastStepVehicleIDs('phase1_3_2')
        for i in vehicle_list_1crossroads:
            if remain_time_1crossroads != 0 and i in controled_bus_1crossroads:
                #任意一条边计算道路长度
                remain_diatance = traci.lane.getLength("phase1_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                diatance_max = remain_time_1crossroads * bus_max_speed
                diatance_min = remain_time_1crossroads * bus_min_speed

                if diatance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                elif diatance_min <= remain_diatance <= diatance_max:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                else:
                    traci.vehicle.setSpeed(i, bus_min_speed)
            elif remain_time_1crossroads == 0 and i in controled_bus_1crossroads:
                # 当前相位为红灯，减速行驶
                remain_diatance = traci.lane.getLength("phase1_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time_1crossroads
                distance_min = bus_min_speed * next_phase_time_1crossroads
                if distance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                elif distance_min <= remain_diatance <= distance_max:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                elif distance_max <= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)

    #---------二号交叉口优先相位公交速度控制----------
    for vehicle_id in vehicle_ids_2crossroads:
        vehicle_type = traci.vehicle.getTypeID(vehicle_id)
        next_route_id = get_next_route_id(vehicle_id)
        if vehicle_type == "bus" and (next_route_id=="E1" or next_route_id=="-824115250#9"):
            if vehicle_id not in controled_bus_2crossroads:
                controled_bus_2crossroads.append(vehicle_id)

        # 二号交叉口南进口道直行
        vehicle_list_2crossroads = traci.lane.getLastStepVehicleIDs('phase2_1_2') + traci.lane.getLastStepVehicleIDs('phase2_1_1')
        for i in vehicle_list_2crossroads:
            if remain_time_2crossroads != 0 and i in controled_bus_2crossroads:
                remain_diatance = traci.lane.getLength("phase2_1_2") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                diatance_max = remain_time_2crossroads * bus_max_speed
                diatance_min = remain_time_2crossroads * bus_min_speed

                if diatance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                elif diatance_min <= remain_diatance <= diatance_max:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                else:
                    traci.vehicle.setSpeed(i, bus_min_speed)
            elif remain_time_2crossroads == 0 and i in controled_bus_2crossroads:
                remain_diatance = traci.lane.getLength("phase2_1_2") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time_2crossroads
                distance_min = bus_min_speed * next_phase_time_2crossroads
                if distance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                elif distance_min <= remain_diatance <= distance_max:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                elif distance_max <= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)

        # 二号交叉口北进口道直行
        vehicle_list_2crossroads = traci.lane.getLastStepVehicleIDs('phase2_3_1') + traci.lane.getLastStepVehicleIDs('phase2_3_2')
        for i in vehicle_list_2crossroads:
            if remain_time_2crossroads != 0 and i in controled_bus_2crossroads:
                # 任意一条边计算道路长度
                remain_diatance = traci.lane.getLength("phase2_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                diatance_max = remain_time_2crossroads * bus_max_speed
                diatance_min = remain_time_2crossroads * bus_min_speed

                if diatance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                elif diatance_min <= remain_diatance <= diatance_max:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                else:
                    traci.vehicle.setSpeed(i, bus_min_speed)
            elif remain_time_2crossroads == 0 and i in controled_bus_2crossroads:
                # 当前相位为红灯，减速行驶
                remain_diatance = traci.lane.getLength("phase2_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time_2crossroads
                distance_min = bus_min_speed * next_phase_time_2crossroads
                if distance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                elif distance_min <= remain_diatance <= distance_max:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                elif distance_max <= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)

    #-------------交叉口三优先相位公交速度控制--------------
    for vehicle_id in vehicle_ids_3crossroads:
        vehicle_type = traci.vehicle.getTypeID(vehicle_id)
        next_route_id = get_next_route_id(vehicle_id)
        if vehicle_type =="bus" and (next_route_id == "797981728#0" or next_route_id=="-192585008#1"):
            if vehicle_id not in controled_bus_3crossroads:
                controled_bus_3crossroads.append(vehicle_id)

        #三号交叉口南进口道直行
        vehicle_list_3crossroads = traci.lane.getLastStepVehicleIDs('phase3_1_2') + traci.lane.getLastStepVehicleIDs('phase3_1_1')
        for i in vehicle_list_3crossroads:
            if remain_time_3crossroads != 0 and i in controled_bus_3crossroads:
                remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i)- car_num_before_bus(i)
                diatance_max = remain_time_3crossroads * bus_max_speed
                diatance_min = remain_time_3crossroads * bus_min_speed

                if diatance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i,bus_max_speed)
                elif diatance_min <= remain_diatance <= diatance_max:
                    traci.vehicle.setSpeed(i,bus_max_speed)
                else:
                    traci.vehicle.setSpeed(i,bus_min_speed)
            elif remain_time_3crossroads == 0 and i in controled_bus_3crossroads:
                remain_diatance = traci.lane.getLength("phase3_1_2") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time_3crossroads
                distance_min = bus_min_speed * next_phase_time_3crossroads
                if distance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i,bus_min_speed)
                elif distance_min <= remain_diatance <= distance_max:
                    traci.vehicle.setSpeed(i,bus_min_speed)
                elif distance_max <= remain_diatance:
                    traci.vehicle.setSpeed(i,bus_max_speed)

        #三号交叉口北进口道直行
        vehicle_list_3crossroads = traci.lane.getLastStepVehicleIDs('phase3_3_1') + traci.lane.getLastStepVehicleIDs('phase3_3_2')
        for i in vehicle_list_3crossroads:
            if remain_time_3crossroads != 0 and i in controled_bus_3crossroads:
                #任意一条边计算道路长度
                remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                diatance_max = remain_time_3crossroads * bus_max_speed
                diatance_min = remain_time_3crossroads * bus_min_speed

                if diatance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                elif diatance_min <= remain_diatance <= diatance_max:
                    traci.vehicle.setSpeed(i, bus_max_speed)
                else:
                    traci.vehicle.setSpeed(i, bus_min_speed)
            elif remain_time_3crossroads == 0 and i in controled_bus_3crossroads:
                # 当前相位为红灯，减速行驶
                remain_diatance = traci.lane.getLength("phase3_3_1") - traci.vehicle.getLanePosition(i) - car_num_before_bus(i)
                distance_max = bus_max_speed * next_phase_time_3crossroads
                distance_min = bus_min_speed * next_phase_time_3crossroads
                if distance_min >= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                elif distance_min <= remain_diatance <= distance_max:
                    traci.vehicle.setSpeed(i, bus_min_speed)
                elif distance_max <= remain_diatance:
                    traci.vehicle.setSpeed(i, bus_max_speed)

    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真分别计算每秒三个交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        #交叉口一等待车辆数
        for phase_name,lane_name in phase_to_lane_1crossroads.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_clxt_1crossroads and vehicle_speed < 0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car":
                            total_person_delay_car_1crossroads = vehicle_waiting_time * person_num_car
                            total_person_delay_clxt_1crossroads += total_person_delay_car_1crossroads
                            cycle_delay_clxt_1crossroads += total_person_delay_car_1crossroads

                            car_count_clxt_1crossroads.append(vehicle_id)
                            vehicle_tracking_clxt_1crossroads.append(vehicle_id)

                        elif vehicle_type=="bus":
                            #总乘客延误时间
                            total_person_delay_bus_1crossroads = vehicle_waiting_time * person_num_bus
                            total_person_delay_clxt_1crossroads += total_person_delay_bus_1crossroads
                            cycle_delay_clxt_1crossroads += total_person_delay_bus_1crossroads
                            #公交车延误时间
                            bus_delay_clxt_1crossroads += vehicle_waiting_time
                            #延误公交车数量
                            bus_count_clxt_1crossroads.append(vehicle_id)
                            vehicle_tracking_clxt_1crossroads.append(vehicle_id)
                    elif vehicle_id in vehicle_tracking_clxt_1crossroads and vehicle_speed >0.1:
                        vehicle_tracking_clxt_1crossroads.remove(vehicle_id)
        #交叉口二等待车辆数
        for phase_name,lane_name in phase_to_lane_2crossroads.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_clxt_2crossroads and vehicle_speed < 0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car":
                            total_person_delay_car_2crossroads = vehicle_waiting_time * person_num_car
                            total_person_delay_clxt_2crossroads += total_person_delay_car_2crossroads
                            cycle_delay_clxt_2crossroads += total_person_delay_car_2crossroads

                            car_count_clxt_2crossroads.append(vehicle_id)
                            vehicle_tracking_clxt_2crossroads.append(vehicle_id)

                        elif vehicle_type=="bus":
                            #总乘客延误时间
                            total_person_delay_bus_2crossroads = vehicle_waiting_time * person_num_bus
                            total_person_delay_clxt_2crossroads += total_person_delay_bus_2crossroads
                            cycle_delay_clxt_2crossroads += total_person_delay_bus_2crossroads
                            #公交车延误时间
                            bus_delay_clxt_2crossroads += vehicle_waiting_time
                            #延误公交车数量
                            bus_count_clxt_2crossroads.append(vehicle_id)
                            vehicle_tracking_clxt_2crossroads.append(vehicle_id)
                    elif vehicle_id in vehicle_tracking_clxt_2crossroads and vehicle_speed >0.1:
                        vehicle_tracking_clxt_2crossroads.remove(vehicle_id)
        #交叉口三等待车辆数
        for phase_name,lane_name in phase_to_lane_3crossroads.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_clxt_3crossroads and vehicle_speed < 0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car":
                            total_person_delay_car_3crossroads = vehicle_waiting_time * person_num_car
                            total_person_delay_clxt_3crossroads += total_person_delay_car_3crossroads
                            cycle_delay_clxt_3crossroads += total_person_delay_car_3crossroads

                            car_count_clxt_3crossroads.append(vehicle_id)
                            vehicle_tracking_clxt_3crossroads.append(vehicle_id)

                        elif vehicle_type=="bus":
                            #总乘客延误时间
                            total_person_delay_bus_3crossroads = vehicle_waiting_time * person_num_bus
                            total_person_delay_clxt_3crossroads += total_person_delay_bus_3crossroads
                            cycle_delay_clxt_3crossroads += total_person_delay_bus_3crossroads
                            #公交车延误时间
                            bus_delay_clxt_3crossroads += vehicle_waiting_time
                            #延误公交车数量
                            bus_count_clxt_3crossroads.append(vehicle_id)
                            vehicle_tracking_clxt_3crossroads.append(vehicle_id)
                    elif vehicle_id in vehicle_tracking_clxt_3crossroads and vehicle_speed >0.1:
                        vehicle_tracking_clxt_3crossroads.remove(vehicle_id)

    if current_time > time_interval_end:
        #统计周期结束时数据
        #一号交叉口
        car_count_cycle_clxt_1crossroads = len(car_count_clxt_1crossroads)
        bus_count_cycle_clxt_1crossroads = len(bus_count_clxt_1crossroads)
        person_count_cycle_clxt_1crossroads = int(car_count_cycle_clxt_1crossroads * person_num_car + bus_count_cycle_clxt_1crossroads * person_num_bus)
        person_delay_time_clxt_1crossroads = int(cycle_delay_clxt_1crossroads)
        bus_delay_cycle_clxt_1crossroads = int(bus_delay_clxt_1crossroads)

        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_clxt_1crossroads}, Buses={bus_count_cycle_clxt_1crossroads}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_clxt_1crossroads}")

        cycles_clxt_1crossroads.append(cycle_counter)
        car_in_cycle_clxt_1crossroads.append(car_count_cycle_clxt_1crossroads)
        bus_in_cycle_clxt_1crossroads.append(bus_count_cycle_clxt_1crossroads)
        person_in_cycle_clxt_1crossroads.append(person_count_cycle_clxt_1crossroads)
        simulation_delay_time_clxt_1crossroads.append(person_delay_time_clxt_1crossroads)
        bus_delay_time_clxt_1crossroads.append(bus_delay_cycle_clxt_1crossroads)

        with open('车路协同下一号交叉口的延误.csv', 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'delay_car', 'delay_bus', 'delay_person', 'delay_time',
                          'bus_delay_time']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for i in range(len(cycles_clxt_1crossroads)):
                writer.writerow({
                    'cycle': cycles_clxt_1crossroads[i],
                    'delay_car': car_in_cycle_clxt_1crossroads[i],
                    'delay_bus': bus_in_cycle_clxt_1crossroads[i],
                    'delay_person': person_in_cycle_clxt_1crossroads[i],
                    'delay_time': simulation_delay_time_clxt_1crossroads[i],
                    'bus_delay_time': bus_delay_time_clxt_1crossroads[i]
                })

        car_count_clxt_1crossroads = []
        bus_count_clxt_1crossroads = []
        cycle_delay_clxt_1crossroads = 0
        bus_delay_clxt_1crossroads = 0

        #二号交叉口
        car_count_cycle_clxt_2crossroads = len(car_count_clxt_2crossroads)
        bus_count_cycle_clxt_2crossroads = len(bus_count_clxt_2crossroads)
        person_count_cycle_clxt_2crossroads = int(car_count_cycle_clxt_2crossroads * person_num_car + bus_count_cycle_clxt_2crossroads * person_num_bus)
        person_delay_time_clxt_2crossroads = int(cycle_delay_clxt_2crossroads)
        bus_delay_cycle_clxt_2crossroads = int(bus_delay_clxt_2crossroads)

        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_clxt_2crossroads}, Buses={bus_count_cycle_clxt_2crossroads}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_clxt_2crossroads}")

        cycles_clxt_2crossroads.append(cycle_counter)
        car_in_cycle_clxt_2crossroads.append(car_count_cycle_clxt_2crossroads)
        bus_in_cycle_clxt_2crossroads.append(bus_count_cycle_clxt_2crossroads)
        person_in_cycle_clxt_2crossroads.append(person_count_cycle_clxt_2crossroads)
        simulation_delay_time_clxt_2crossroads.append(person_delay_time_clxt_2crossroads)
        bus_delay_time_clxt_2crossroads.append(bus_delay_cycle_clxt_2crossroads)

        with open('车路协同下二号交叉口的延误.csv', 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'delay_car', 'delay_bus', 'delay_person', 'delay_time',
                          'bus_delay_time']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for i in range(len(cycles_clxt_2crossroads)):
                writer.writerow({
                    'cycle': cycles_clxt_2crossroads[i],
                    'delay_car': car_in_cycle_clxt_2crossroads[i],
                    'delay_bus': bus_in_cycle_clxt_2crossroads[i],
                    'delay_person': person_in_cycle_clxt_2crossroads[i],
                    'delay_time': simulation_delay_time_clxt_2crossroads[i],
                    'bus_delay_time': bus_delay_time_clxt_2crossroads[i]
                })

        car_count_clxt_2crossroads = []
        bus_count_clxt_2crossroads = []
        cycle_delay_clxt_2crossroads = 0
        bus_delay_clxt_2crossroads = 0

        #三号交叉口
        car_count_cycle_clxt_3crossroads = len(car_count_clxt_3crossroads)
        bus_count_cycle_clxt_3crossroads = len(bus_count_clxt_3crossroads)
        person_count_cycle_clxt_3crossroads = int(car_count_cycle_clxt_3crossroads * person_num_car + bus_count_cycle_clxt_3crossroads * person_num_bus)
        person_delay_time_clxt_3crossroads = int(cycle_delay_clxt_3crossroads)
        bus_delay_cycle_clxt_3crossroads = int(bus_delay_clxt_3crossroads)

        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_clxt_3crossroads}, Buses={bus_count_cycle_clxt_3crossroads}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_clxt_3crossroads}")

        cycles_clxt_3crossroads.append(cycle_counter)
        car_in_cycle_clxt_3crossroads.append(car_count_cycle_clxt_3crossroads)
        bus_in_cycle_clxt_3crossroads.append(bus_count_cycle_clxt_3crossroads)
        person_in_cycle_clxt_3crossroads.append(person_count_cycle_clxt_3crossroads)
        simulation_delay_time_clxt_3crossroads.append(person_delay_time_clxt_3crossroads)
        bus_delay_time_clxt_3crossroads.append(bus_delay_cycle_clxt_3crossroads)

        with open('车路协同下三号交叉口的延误.csv', 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'delay_car', 'delay_bus', 'delay_person', 'delay_time',
                          'bus_delay_time']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for i in range(len(cycles_clxt_3crossroads)):
                writer.writerow({
                    'cycle': cycles_clxt_3crossroads[i],
                    'delay_car': car_in_cycle_clxt_3crossroads[i],
                    'delay_bus': bus_in_cycle_clxt_3crossroads[i],
                    'delay_person': person_in_cycle_clxt_3crossroads[i],
                    'delay_time': simulation_delay_time_clxt_3crossroads[i],
                    'bus_delay_time': bus_delay_time_clxt_3crossroads[i]
                })

        car_count_clxt_3crossroads = []
        bus_count_clxt_3crossroads = []
        cycle_delay_clxt_3crossroads = 0
        bus_delay_clxt_3crossroads = 0


        cycle_start_time = time_interval_end

traci.close()


#3-初始信号配时
# 配时方案
signal_plan_before_1crossroads = pd.read_csv('C:/Users/<USER>/Desktop/毕业/TSP/signal_time/初始信号配时方案_交叉口1.csv')
signal_plan_before_2crossroads = pd.read_csv('C:/Users/<USER>/Desktop/毕业/TSP/signal_time/初始信号配时方案_交叉口2.csv')
signal_plan_before_3crossroads = pd.read_csv('C:/Users/<USER>/Desktop/毕业/TSP/signal_time/初始信号配时方案_交叉口3.csv')
trigger_time_list_before_1crossroads = signal_plan_before_1crossroads['Cycle'].unique().tolist()
trigger_time_list_before_2crossroads = signal_plan_before_2crossroads['Cycle'].unique().tolist()
trigger_time_list_before_3crossroads = signal_plan_before_3crossroads['Cycle'].unique().tolist()

traci.start([sumo_binary, "-c", sumo_cfg_file])

#一号交叉口信号接收
phase_duration_dict_before_1crossroads = signal_plan_before_1crossroads.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()
logic_dict_before_1crossroads = {}
for phase_time, duration_dict in phase_duration_dict_before_1crossroads.items():
    phase_list = []
    for phase, state in phase_dict_1crossroads.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))
    logic_dict_before_1crossroads.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#二号交叉口信号接收
phase_duration_dict_before_2crossroads = signal_plan_before_2crossroads.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()
logic_dict_before_2crossroads = {}
for phase_time, duration_dict in phase_duration_dict_before_2crossroads.items():
    phase_list = []
    for phase, state in phase_dict_2crossroads.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))
    logic_dict_before_2crossroads.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})

#三号交叉口信号接收
phase_duration_dict_before_3crossroads = signal_plan_before_3crossroads.groupby('Cycle').apply(lambda x: dict(zip(x['Phase'], x['Green_Light_Time']))).to_dict()
logic_dict_before_3crossroads = {}
for phase_time, duration_dict in phase_duration_dict_before_3crossroads.items():
    phase_list = []
    for phase, state in phase_dict_3crossroads.items():
        phase_list.append(sumolib.net.Phase(duration=duration_dict[int(phase)], state=state, name=phase))
    logic_dict_before_3crossroads.update({phase_time: traci.trafficlight.Logic(programID=str(phase_time), phases=tuple(phase_list), type=0,currentPhaseIndex=0)})


vehicle_tracking_before_1crossroads=[]
vehicle_tracking_before_2crossroads=[]
vehicle_tracking_before_3crossroads=[]

cycle_counter=1
current_time = 0
cycle_start_time=current_time
track_cycle = []

car_count_before_1crossroads = []
car_count_before_2crossroads = []
car_count_before_3crossroads = []

bus_count_before_1crossroads = []
bus_count_before_2crossroads = []
bus_count_before_3crossroads = []

cycles_before_1crossroads=[]
cycles_before_2crossroads=[]
cycles_before_3crossroads=[]
car_in_cycle_before_1crossroads=[]
car_in_cycle_before_2crossroads=[]
car_in_cycle_before_3crossroads=[]

bus_in_cycle_before_1crossroads=[]
bus_in_cycle_before_2crossroads=[]
bus_in_cycle_before_3crossroads=[]

person_in_cycle_before_1crossroads=[]
person_in_cycle_before_2crossroads=[]
person_in_cycle_before_3crossroads=[]

#计算乘客延误时间
total_person_delay_before_1crossroads=0
total_person_delay_before_2crossroads=0
total_person_delay_before_3crossroads=0

bus_delay_before_1crossroads = 0
bus_delay_before_2crossroads = 0
bus_delay_before_3crossroads = 0

cycle_delay_before_1crossroads = 0
cycle_delay_before_2crossroads = 0
cycle_delay_before_3crossroads = 0

simulation_delay_time_before_1crossroads = []
simulation_delay_time_before_2crossroads = []
simulation_delay_time_before_3crossroads = []

average_delay_time_before_1crossroads = []
average_delay_time_before_2crossroads = []
average_delay_time_before_3crossroads = []

bus_delay_time_before_1crossroads = []
bus_delay_time_before_2crossroads = []
bus_delay_time_before_3crossroads = []

# while traci.simulation.getMinExpectedNumber() > 0:
while traci.simulation.getTime()<500:
    cycle_counter = current_time // signal_cycle + 1
    # 仿真开始前输入信号配时方案
    if current_time == 0:
        for phase_time, logic in logic_dict_before_1crossroads.items():
            traci.trafficlight.setProgramLogic('light1', logic)
        for phase_time, logic in logic_dict_before_2crossroads.items():
            traci.trafficlight.setProgramLogic('light2', logic)
        for phase_time, logic in logic_dict_before_3crossroads.items():
            traci.trafficlight.setProgramLogic('light3', logic)

    # 切换配时方案
    if cycle_counter in trigger_time_list_before_1crossroads:
        if cycle_counter not in track_cycle:
            traci.trafficlight.setProgram('light1', str(cycle_counter))
            traci.trafficlight.setProgram('light2', str(cycle_counter))
            traci.trafficlight.setProgram('light3', str(cycle_counter))
            track_cycle.append(cycle_counter)
    # 开始仿真
    traci.simulationStep()
    current_time += 1

    #计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    #开始仿真分别计算每秒三个交叉口等待车辆数
    if time_interval_start <=  current_time <= time_interval_end:
        #交叉口一等待车辆数
        for phase_name,lane_name in phase_to_lane_1crossroads.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_before_1crossroads and vehicle_speed<0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car":
                            total_person_delay_car_1crossroads = vehicle_waiting_time * person_num_car
                            total_person_delay_before_1crossroads += total_person_delay_car_1crossroads
                            cycle_delay_before_1crossroads += total_person_delay_car_1crossroads

                            car_count_before_1crossroads.append(vehicle_id)
                            vehicle_tracking_before_1crossroads.append(vehicle_id)

                        elif vehicle_type=="bus":
                            #总乘客延误时间
                            total_person_delay_bus_1crossroads = vehicle_waiting_time * person_num_bus
                            total_person_delay_before_1crossroads += total_person_delay_bus_1crossroads
                            cycle_delay_before_1crossroads += total_person_delay_bus_1crossroads
                            #公交车延误时间
                            bus_delay_before_1crossroads += vehicle_waiting_time
                            #延误社会车数量
                            bus_count_before_1crossroads.append(vehicle_id)
                            vehicle_tracking_before_1crossroads.append(vehicle_id)

                    elif vehicle_id in vehicle_tracking_before_1crossroads and vehicle_speed >0.1:
                        vehicle_tracking_before_1crossroads.remove(vehicle_id)
        #交叉口二等待车辆数
        for phase_name,lane_name in phase_to_lane_2crossroads.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_before_2crossroads and vehicle_speed<0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car":
                            total_person_delay_car_2crossroads = vehicle_waiting_time * person_num_car
                            total_person_delay_before_2crossroads += total_person_delay_car_2crossroads
                            cycle_delay_before_2crossroads += total_person_delay_car_2crossroads

                            car_count_before_2crossroads.append(vehicle_id)
                            vehicle_tracking_before_2crossroads.append(vehicle_id)

                        elif vehicle_type=="bus":
                            #总乘客延误时间
                            total_person_delay_bus_2crossroads = vehicle_waiting_time * person_num_bus
                            total_person_delay_before_2crossroads += total_person_delay_bus_2crossroads
                            cycle_delay_before_2crossroads += total_person_delay_bus_2crossroads
                            #公交车延误时间
                            bus_delay_before_2crossroads += vehicle_waiting_time
                            #延误社会车数量
                            bus_count_before_2crossroads.append(vehicle_id)
                            vehicle_tracking_before_2crossroads.append(vehicle_id)

                    elif vehicle_id in vehicle_tracking_before_2crossroads and vehicle_speed >0.1:
                        vehicle_tracking_before_2crossroads.remove(vehicle_id)
        #交叉口三等待车辆数
        for phase_name,lane_name in phase_to_lane_3crossroads.items():
            for lane in lane_name:
                vehicle_list=traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type=traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed=traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_before_3crossroads and vehicle_speed<0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type=="car":
                            total_person_delay_car_3crossroads = vehicle_waiting_time * person_num_car
                            total_person_delay_before_3crossroads += total_person_delay_car_3crossroads
                            cycle_delay_before_3crossroads += total_person_delay_car_3crossroads

                            car_count_before_3crossroads.append(vehicle_id)
                            vehicle_tracking_before_3crossroads.append(vehicle_id)

                        elif vehicle_type=="bus":
                            #总乘客延误时间
                            total_person_delay_bus_3crossroads = vehicle_waiting_time * person_num_bus
                            total_person_delay_before_3crossroads += total_person_delay_bus_3crossroads
                            cycle_delay_before_3crossroads += total_person_delay_bus_3crossroads
                            #公交车延误时间
                            bus_delay_before_3crossroads += vehicle_waiting_time
                            #延误社会车数量
                            bus_count_before_3crossroads.append(vehicle_id)
                            vehicle_tracking_before_3crossroads.append(vehicle_id)

                    elif vehicle_id in vehicle_tracking_before_3crossroads and vehicle_speed >0.1:
                        vehicle_tracking_before_3crossroads.remove(vehicle_id)

    if current_time > time_interval_end:
        #统计周期结束时的车辆数量
        #一号交叉口
        car_count_cycle_before_1crossroads=len(car_count_before_1crossroads)
        bus_count_cycle_before_1crossroads=len(bus_count_before_1crossroads)
        person_count_cycle_before_1crossroads=int(car_count_cycle_before_1crossroads * person_num_car + bus_count_cycle_before_1crossroads * person_num_bus)
        person_delay_time_before_1crossroads = int(cycle_delay_before_1crossroads)
        bus_delay_cycle_before_1crossroads = int(bus_delay_before_1crossroads)
        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_before_1crossroads}, Buses={bus_count_cycle_before_1crossroads}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_before_1crossroads}")

        cycles_before_1crossroads.append(cycle_counter)
        car_in_cycle_before_1crossroads.append(car_count_cycle_before_1crossroads)
        bus_in_cycle_before_1crossroads.append(bus_count_cycle_before_1crossroads)
        person_in_cycle_before_1crossroads.append(person_count_cycle_before_1crossroads)
        simulation_delay_time_before_1crossroads.append(person_delay_time_before_1crossroads)
        bus_delay_time_before_1crossroads.append(bus_delay_cycle_before_1crossroads)

        with open('基于车流下一号交叉口的延误.csv', 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'delay_car', 'delay_bus', 'delay_person', 'delay_time',
                          'bus_delay_time']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for i in range(len(cycles_before_1crossroads)):
                writer.writerow({
                    'cycle': cycles_before_1crossroads[i],
                    'delay_car': car_in_cycle_before_1crossroads[i],
                    'delay_bus': bus_in_cycle_before_1crossroads[i],
                    'delay_person': person_in_cycle_before_1crossroads[i],
                    'delay_time': simulation_delay_time_before_1crossroads[i],
                    'bus_delay_time': bus_delay_time_before_1crossroads[i]
                })

        car_count_before_1crossroads = []
        bus_count_before_1crossroads = []
        cycle_delay_before_1crossroads = 0
        bus_delay_before_1crossroads = 0

        #二号交叉口
        car_count_cycle_before_2crossroads=len(car_count_before_2crossroads)
        bus_count_cycle_before_2crossroads=len(bus_count_before_2crossroads)
        person_count_cycle_before_2crossroads=int(car_count_cycle_before_2crossroads * person_num_car + bus_count_cycle_before_2crossroads * person_num_bus)
        person_delay_time_before_2crossroads = int(cycle_delay_before_2crossroads)
        bus_delay_cycle_before_2crossroads = int(bus_delay_before_2crossroads)
        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_before_2crossroads}, Buses={bus_count_cycle_before_2crossroads}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_before_2crossroads}")

        cycles_before_2crossroads.append(cycle_counter)
        car_in_cycle_before_2crossroads.append(car_count_cycle_before_2crossroads)
        bus_in_cycle_before_2crossroads.append(bus_count_cycle_before_2crossroads)
        person_in_cycle_before_2crossroads.append(person_count_cycle_before_2crossroads)
        simulation_delay_time_before_2crossroads.append(person_delay_time_before_2crossroads)
        bus_delay_time_before_2crossroads.append(bus_delay_cycle_before_2crossroads)

        with open('基于车流下二号交叉口的延误.csv', 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'delay_car', 'delay_bus', 'delay_person', 'delay_time',
                          'bus_delay_time']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for i in range(len(cycles_before_2crossroads)):
                writer.writerow({
                    'cycle': cycles_before_2crossroads[i],
                    'delay_car': car_in_cycle_before_2crossroads[i],
                    'delay_bus': bus_in_cycle_before_2crossroads[i],
                    'delay_person': person_in_cycle_before_2crossroads[i],
                    'delay_time': simulation_delay_time_before_2crossroads[i],
                    'bus_delay_time': bus_delay_time_before_2crossroads[i]
                })

        car_count_before_2crossroads = []
        bus_count_before_2crossroads = []
        cycle_delay_before_2crossroads = 0
        bus_delay_before_2crossroads = 0

        #三号交叉口
        car_count_cycle_before_3crossroads=len(car_count_before_3crossroads)
        bus_count_cycle_before_3crossroads=len(bus_count_before_3crossroads)
        person_count_cycle_before_3crossroads=int(car_count_cycle_before_3crossroads * person_num_car + bus_count_cycle_before_3crossroads * person_num_bus)
        person_delay_time_before_3crossroads = int(cycle_delay_before_3crossroads)
        if person_count_cycle_before_3crossroads != 0:
            average_delay_before_3crossroads = int(person_delay_time_before_3crossroads / person_count_cycle_before_3crossroads)
        else:
            average_delay_before_3crossroads =0
        bus_delay_cycle_before_3crossroads = int(bus_delay_before_3crossroads)
        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_before_3crossroads}, Buses={bus_count_cycle_before_3crossroads}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_before_3crossroads}")

        cycles_before_3crossroads.append(cycle_counter)
        car_in_cycle_before_3crossroads.append(car_count_cycle_before_3crossroads)
        bus_in_cycle_before_3crossroads.append(bus_count_cycle_before_3crossroads)
        person_in_cycle_before_3crossroads.append(person_count_cycle_before_3crossroads)
        simulation_delay_time_before_3crossroads.append(person_delay_time_before_3crossroads)
        average_delay_time_before_3crossroads.append(average_delay_before_3crossroads)
        bus_delay_time_before_3crossroads.append(bus_delay_cycle_before_3crossroads)

        with open('基于车流下三号交叉口的延误.csv', 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'delay_car', 'delay_bus', 'delay_person', 'delay_time',
                          'bus_delay_time']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for i in range(len(cycles_before_3crossroads)):
                writer.writerow({
                    'cycle': cycles_before_3crossroads[i],
                    'delay_car': car_in_cycle_before_3crossroads[i],
                    'delay_bus': bus_in_cycle_before_3crossroads[i],
                    'delay_person': person_in_cycle_before_3crossroads[i],
                    'delay_time': simulation_delay_time_before_3crossroads[i],
                    'bus_delay_time': bus_delay_time_before_3crossroads[i]
                })

        car_count_before_3crossroads = []
        bus_count_before_3crossroads = []
        cycle_delay_before_3crossroads = 0
        bus_delay_before_3crossroads = 0

        cycle_start_time = time_interval_end

traci.close()



#无信号优化情况下的延误
traci.start([sumo_binary, "-c", sumo_cfg_file])

vehicle_tracking_no_1crossroads = []
vehicle_tracking_no_2crossroads = []
vehicle_tracking_no_3crossroads = []

cycle_counter = 1
current_time = 0
cycle_start_time = current_time
track_cycle = []

car_count_no_1crossroads = []
car_count_no_2crossroads = []
car_count_no_3crossroads = []

bus_count_no_1crossroads = []
bus_count_no_2crossroads = []
bus_count_no_3crossroads = []

cycles_no_1crossroads = []
cycles_no_2crossroads = []
cycles_no_3crossroads = []
car_in_cycle_no_1crossroads = []
car_in_cycle_no_2crossroads = []
car_in_cycle_no_3crossroads = []

bus_in_cycle_no_1crossroads = []
bus_in_cycle_no_2crossroads = []
bus_in_cycle_no_3crossroads = []

person_in_cycle_no_1crossroads = []
person_in_cycle_no_2crossroads = []
person_in_cycle_no_3crossroads = []

# 计算乘客延误时间
total_person_delay_no_1crossroads = 0
total_person_delay_no_2crossroads = 0
total_person_delay_no_3crossroads = 0

bus_delay_no_1crossroads = 0
bus_delay_no_2crossroads = 0
bus_delay_no_3crossroads = 0

cycle_delay_no_1crossroads = 0
cycle_delay_no_2crossroads = 0
cycle_delay_no_3crossroads = 0

simulation_delay_time_no_1crossroads = []
simulation_delay_time_no_2crossroads = []
simulation_delay_time_no_3crossroads = []

average_delay_time_no_1crossroads = []
average_delay_time_no_2crossroads = []
average_delay_time_no_3crossroads = []

bus_delay_time_no_1crossroads = []
bus_delay_time_no_2crossroads = []
bus_delay_time_no_3crossroads = []

# while traci.simulation.getMinExpectedNumber() > 0:
while traci.simulation.getTime()<500:
    cycle_counter = current_time // signal_cycle + 1

    # 开始仿真
    traci.simulationStep()
    current_time += 1

    # 计算周期开始和结束时间
    time_interval_start = cycle_start_time
    time_interval_end = cycle_start_time + signal_cycle

    # 开始仿真分别计算每秒三个交叉口等待车辆数
    if time_interval_start <= current_time <= time_interval_end:
        # 交叉口一等待车辆数
        for phase_name, lane_name in phase_to_lane_1crossroads.items():
            for lane in lane_name:
                vehicle_list = traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type = traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed = traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_no_1crossroads and vehicle_speed < 0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type == "car":
                            total_person_delay_car_1crossroads = vehicle_waiting_time * person_num_car
                            total_person_delay_no_1crossroads += total_person_delay_car_1crossroads
                            cycle_delay_no_1crossroads += total_person_delay_car_1crossroads

                            car_count_no_1crossroads.append(vehicle_id)
                            vehicle_tracking_no_1crossroads.append(vehicle_id)

                        elif vehicle_type == "bus":
                            # 总乘客延误时间
                            total_person_delay_bus_1crossroads = vehicle_waiting_time * person_num_bus
                            total_person_delay_no_1crossroads += total_person_delay_bus_1crossroads
                            cycle_delay_no_1crossroads += total_person_delay_bus_1crossroads
                            # 公交车延误时间
                            bus_delay_no_1crossroads += vehicle_waiting_time
                            # 延误社会车数量
                            bus_count_no_1crossroads.append(vehicle_id)
                            vehicle_tracking_no_1crossroads.append(vehicle_id)

                    elif vehicle_id in vehicle_tracking_no_1crossroads and vehicle_speed > 0.1:
                        vehicle_tracking_no_1crossroads.remove(vehicle_id)
        # 交叉口二等待车辆数
        for phase_name, lane_name in phase_to_lane_2crossroads.items():
            for lane in lane_name:
                vehicle_list = traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type = traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed = traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_no_2crossroads and vehicle_speed < 0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type == "car":
                            total_person_delay_car_2crossroads = vehicle_waiting_time * person_num_car
                            total_person_delay_no_2crossroads += total_person_delay_car_2crossroads
                            cycle_delay_no_2crossroads += total_person_delay_car_2crossroads

                            car_count_no_2crossroads.append(vehicle_id)
                            vehicle_tracking_no_2crossroads.append(vehicle_id)

                        elif vehicle_type == "bus":
                            # 总乘客延误时间
                            total_person_delay_bus_2crossroads = vehicle_waiting_time * person_num_bus
                            total_person_delay_no_2crossroads += total_person_delay_bus_2crossroads
                            cycle_delay_no_2crossroads += total_person_delay_bus_2crossroads
                            # 公交车延误时间
                            bus_delay_no_2crossroads += vehicle_waiting_time
                            # 延误社会车数量
                            bus_count_no_2crossroads.append(vehicle_id)
                            vehicle_tracking_no_2crossroads.append(vehicle_id)

                    elif vehicle_id in vehicle_tracking_no_2crossroads and vehicle_speed > 0.1:
                        vehicle_tracking_no_2crossroads.remove(vehicle_id)
        # 交叉口三等待车辆数
        for phase_name, lane_name in phase_to_lane_3crossroads.items():
            for lane in lane_name:
                vehicle_list = traci.lane.getLastStepVehicleIDs(lane)

                for vehicle_id in vehicle_list:
                    vehicle_type = traci.vehicle.getTypeID(vehicle_id)
                    vehicle_speed = traci.vehicle.getSpeed(vehicle_id)
                    if vehicle_id not in vehicle_tracking_no_3crossroads and vehicle_speed < 0.1:
                        vehicle_waiting_time = traci.lane.getWaitingTime(lane)
                        if vehicle_type == "car":
                            total_person_delay_car_3crossroads = vehicle_waiting_time * person_num_car
                            total_person_delay_no_3crossroads += total_person_delay_car_3crossroads
                            cycle_delay_no_3crossroads += total_person_delay_car_3crossroads

                            car_count_no_3crossroads.append(vehicle_id)
                            vehicle_tracking_no_3crossroads.append(vehicle_id)

                        elif vehicle_type == "bus":
                            # 总乘客延误时间
                            total_person_delay_bus_3crossroads = vehicle_waiting_time * person_num_bus
                            total_person_delay_no_3crossroads += total_person_delay_bus_3crossroads
                            cycle_delay_no_3crossroads += total_person_delay_bus_3crossroads
                            # 公交车延误时间
                            bus_delay_no_3crossroads += vehicle_waiting_time
                            # 延误社会车数量
                            bus_count_no_3crossroads.append(vehicle_id)
                            vehicle_tracking_no_3crossroads.append(vehicle_id)

                    elif vehicle_id in vehicle_tracking_no_3crossroads and vehicle_speed > 0.1:
                        vehicle_tracking_no_3crossroads.remove(vehicle_id)

    if current_time > time_interval_end:
        # 统计周期结束时的车辆数量
        # 一号交叉口
        car_count_cycle_no_1crossroads = len(car_count_no_1crossroads)
        bus_count_cycle_no_1crossroads = len(bus_count_no_1crossroads)
        person_count_cycle_no_1crossroads = int(
            car_count_cycle_no_1crossroads * person_num_car + bus_count_cycle_no_1crossroads * person_num_bus)
        person_delay_time_no_1crossroads = int(cycle_delay_no_1crossroads)
        if person_count_cycle_no_1crossroads != 0:
            average_delay_no_1crossroads = int(person_delay_time_no_1crossroads / person_count_cycle_no_1crossroads)
        else:
            average_delay_no_1crossroads = 0
        bus_delay_cycle_no_1crossroads = int(bus_delay_no_1crossroads)
        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_no_1crossroads}, Buses={bus_count_cycle_no_1crossroads}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_no_1crossroads}")

        cycles_no_1crossroads.append(cycle_counter)
        car_in_cycle_no_1crossroads.append(car_count_cycle_no_1crossroads)
        bus_in_cycle_no_1crossroads.append(bus_count_cycle_no_1crossroads)
        person_in_cycle_no_1crossroads.append(person_count_cycle_no_1crossroads)
        simulation_delay_time_no_1crossroads.append(person_delay_time_no_1crossroads)
        average_delay_time_no_1crossroads.append(average_delay_no_1crossroads)
        bus_delay_time_no_1crossroads.append(bus_delay_cycle_no_1crossroads)

        with open('初始信号下一号交叉口的延误.csv', 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'delay_car', 'delay_bus', 'delay_person', 'delay_time',
                          'bus_delay_time']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for i in range(len(cycles_no_1crossroads)):
                writer.writerow({
                    'cycle': cycles_no_1crossroads[i],
                    'delay_car': car_in_cycle_no_1crossroads[i],
                    'delay_bus': bus_in_cycle_no_1crossroads[i],
                    'delay_person': person_in_cycle_no_1crossroads[i],
                    'delay_time': simulation_delay_time_no_1crossroads[i],
                    'bus_delay_time': bus_delay_time_no_1crossroads[i]
                })

        car_count_no_1crossroads = []
        bus_count_no_1crossroads = []
        cycle_delay_no_1crossroads = 0
        bus_delay_no_1crossroads = 0

        # 二号交叉口
        car_count_cycle_no_2crossroads = len(car_count_no_2crossroads)
        bus_count_cycle_no_2crossroads = len(bus_count_no_2crossroads)
        person_count_cycle_no_2crossroads = int(
            car_count_cycle_no_2crossroads * person_num_car + bus_count_cycle_no_2crossroads * person_num_bus)
        person_delay_time_no_2crossroads = int(cycle_delay_no_2crossroads)
        bus_delay_cycle_no_2crossroads = int(bus_delay_no_2crossroads)
        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_no_2crossroads}, Buses={bus_count_cycle_no_2crossroads}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_no_2crossroads}")

        cycles_no_2crossroads.append(cycle_counter)
        car_in_cycle_no_2crossroads.append(car_count_cycle_no_2crossroads)
        bus_in_cycle_no_2crossroads.append(bus_count_cycle_no_2crossroads)
        person_in_cycle_no_2crossroads.append(person_count_cycle_no_2crossroads)
        simulation_delay_time_no_2crossroads.append(person_delay_time_no_2crossroads)
        bus_delay_time_no_2crossroads.append(bus_delay_cycle_no_2crossroads)

        with open('初始信号下二号交叉口的延误.csv', 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'delay_car', 'delay_bus', 'delay_person', 'delay_time',
                          'bus_delay_time']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for i in range(len(cycles_no_2crossroads)):
                writer.writerow({
                    'cycle': cycles_no_2crossroads[i],
                    'delay_car': car_in_cycle_no_2crossroads[i],
                    'delay_bus': bus_in_cycle_no_2crossroads[i],
                    'delay_person': person_in_cycle_no_2crossroads[i],
                    'delay_time': simulation_delay_time_no_2crossroads[i],
                    'bus_delay_time': bus_delay_time_no_2crossroads[i]
                })

        car_count_no_2crossroads = []
        bus_count_no_2crossroads = []
        cycle_delay_no_2crossroads = 0
        bus_delay_no_2crossroads = 0

        # 三号交叉口
        car_count_cycle_no_3crossroads = len(car_count_no_3crossroads)
        bus_count_cycle_no_3crossroads = len(bus_count_no_3crossroads)
        person_count_cycle_no_3crossroads = int(
            car_count_cycle_no_3crossroads * person_num_car + bus_count_cycle_no_3crossroads * person_num_bus)
        person_delay_time_no_3crossroads = int(cycle_delay_no_3crossroads)
        bus_delay_cycle_no_3crossroads = int(bus_delay_no_3crossroads)
        print(f"Cycle {cycle_counter}: Cars={car_count_cycle_no_3crossroads}, Buses={bus_count_cycle_no_3crossroads}")
        print(f"Cycle {cycle_counter}: delay_person={person_count_cycle_no_3crossroads}")

        cycles_no_3crossroads.append(cycle_counter)
        car_in_cycle_no_3crossroads.append(car_count_cycle_no_3crossroads)
        bus_in_cycle_no_3crossroads.append(bus_count_cycle_no_3crossroads)
        person_in_cycle_no_3crossroads.append(person_count_cycle_no_3crossroads)
        simulation_delay_time_no_3crossroads.append(person_delay_time_no_3crossroads)
        bus_delay_time_no_3crossroads.append(bus_delay_cycle_no_3crossroads)

        with open('初始信号下三号交叉口的延误.csv', 'w', newline='') as csvfile:
            fieldnames = ['cycle', 'delay_car', 'delay_bus', 'delay_person', 'delay_time',
                          'bus_delay_time']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for i in range(len(cycles_no_3crossroads)):
                writer.writerow({
                    'cycle': cycles_no_3crossroads[i],
                    'delay_car': car_in_cycle_no_3crossroads[i],
                    'delay_bus': bus_in_cycle_no_3crossroads[i],
                    'delay_person': person_in_cycle_no_3crossroads[i],
                    'delay_time': simulation_delay_time_no_3crossroads[i],
                    'bus_delay_time': bus_delay_time_no_3crossroads[i]
                })

        car_count_no_3crossroads = []
        bus_count_no_3crossroads = []
        cycle_delay_no_3crossroads = 0
        bus_delay_no_3crossroads = 0

        cycle_start_time = time_interval_end

traci.close()


# #初始信号控制
# cycles_no = cycles_clxt[:45]
# car_before = car_in_cycle_before[:45]
# bus_before = bus_in_cycle_before[:45]
# bus_before_delay = bus_delay_time_before[:45]
# person_before = person_in_cycle_before[:45]
# total_delay_time_before = simulation_delay_time_before[:45]
#
# #车路协同控制
# car_clxt = car_in_cycle_clxt[:45]
# bus_clxt = bus_in_cycle_clxt[:45]
# bus_clxt_delay = bus_delay_time_clxt[:45]
# person_clxt = person_in_cycle_clxt[:45]
# total_delay_time_clxt = simulation_delay_time_clxt[:45]
#
#
#
# index = np.arange(len(cycles_no))
# # 创建散点图（延误社会车数量）
# plt.figure(figsize=(12, 6))
# plt.scatter(index,car_before, label="初始信号", s=30, marker='o', color='gray')
# plt.scatter(index,car_clxt, label="车路协同控制", s=30, marker='*', color='red')
#
# # 用细线连接散点
# plt.plot(index, car_before, linestyle='-', color='gray', alpha=0.15)
# plt.plot(index, car_clxt, linestyle='-', color='red', alpha=0.15)
#
# # 设置图表标题和标签
# plt.xlabel("周期(cycle)" ,fontproperties=font)
# plt.ylabel('延误社会车辆数(veh)' ,fontproperties=font)
# plt.title('不同方案下延误社会车辆数' ,fontproperties=font)
# # 设置x轴刻度
# plt.xticks(index,cycles_no)
# # 添加图例
# plt.legend(loc='upper right', prop=font)
# plt.show()
#
#
# # 延误公交车数量
# angles = np.linspace(0, 2*np.pi, len(cycles_no), endpoint=False).tolist()
# stats_bus_before = bus_before
# stats_bus_clxt = bus_clxt
#
# fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(polar=True))
# ax.fill(angles, stats_bus_before, color='gray', alpha=0.25, label='初始信号')
# ax.fill(angles, stats_bus_clxt, color='red', alpha=0.25, label='车路协同控制')
#
# ax.set_yticklabels(["{:.0f}".format(i) for i in ax.get_yticks()])  # 添加公交车数值刻度
# ax.set_xticks(angles)
# ax.set_xticklabels(cycles_no)
# plt.title('不同方案下延误公交车对比', fontproperties=font)
# plt.legend(loc='upper right', prop=font)
# plt.show()
#
# #公交车等待时间
# angles = np.linspace(0, 2*np.pi, len(cycles_no), endpoint=False).tolist()
# stats_bus_before = bus_before_delay
# stats_bus_clxt = bus_clxt_delay
#
# fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(polar=True))
# ax.fill(angles, stats_bus_before, color='gray', alpha=0.25, label='初始信号')
# ax.fill(angles, stats_bus_clxt, color='red', alpha=0.25, label='车路协同控制')
#
# ax.set_yticklabels(["{:.0f}".format(i) for i in ax.get_yticks()])  # 添加公交车数值刻度
# ax.set_xticks(angles)
# ax.set_xticklabels(cycles_no)
# plt.title('不同方案下公交车等待时长', fontproperties=font)
# plt.legend(loc='upper right', prop=font)
# plt.show()
#
#
# #延误人数
# data = [person_before,person_clxt]
# plt.figure(figsize=(10, 6))
# box = plt.boxplot(data, patch_artist=True, medianprops=dict(color="black", linewidth=2))
# colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightsalmon']
#
# # 为每个箱体设置不同的颜色
# for patch, color in zip(box['boxes'], colors):
#     patch.set_facecolor(color)
#
# # 设置中文标签
# plt.xticks([1, 2, 3, 4], ['初始信号', '公交优先信号', '速度控制', '车路协同控制'], fontproperties=font)
# plt.ylabel('延误人数(ped)', fontproperties=font)
# plt.title('不同方案下的延误人数对比', fontproperties=font)
# plt.show()
#
#
# #总乘客延误时间
# data = [total_delay_time_before, total_delay_time_clxt]
# plt.figure(figsize=(10, 6))
# box = plt.boxplot(data, patch_artist=True, medianprops=dict(color="black", linewidth=2))
# colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightsalmon']
#
# # 为每个箱体设置不同的颜色
# for patch, color in zip(box['boxes'], colors):
#     patch.set_facecolor(color)
# # 设置中文标签
# plt.xticks([1, 2, 3, 4], ['初始信号', '公交优先信号', '速度控制', '车路协同控制'], fontproperties=font)
# plt.ylabel('总乘客延误时长(s)', fontproperties=font)
# plt.title('不同方案下的总乘客延误时长对比', fontproperties=font)
# plt.show()
# #

